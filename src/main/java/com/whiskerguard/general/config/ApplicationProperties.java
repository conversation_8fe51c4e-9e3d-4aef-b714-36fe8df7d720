package com.whiskerguard.general.config;

import com.whiskerguard.general.model.SmsProviderType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 通用配置属性类
 * <p>
 * 用于绑定 application.yml 中的 application 前缀的配置项
 * </p>
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = false)
public class ApplicationProperties {

    private final Liquibase liquibase = new Liquibase();
    private final Notification notification = new Notification();
    private final ElectronicSignature electronicSignature = new ElectronicSignature();
    private final VerificationCode verificationCode = new VerificationCode();

    // jhipster-needle-application-properties-property

    public Liquibase getLiquibase() {
        return liquibase;
    }

    public Notification getNotification() {
        return notification;
    }

    public ElectronicSignature getElectronicSignature() {
        return electronicSignature;
    }

    public VerificationCode getVerificationCode() {
        return verificationCode;
    }

    // jhipster-needle-application-properties-property-getter

    public static class Liquibase {

        private Boolean asyncStart = true;

        public Boolean getAsyncStart() {
            return asyncStart;
        }

        public void setAsyncStart(Boolean asyncStart) {
            this.asyncStart = asyncStart;
        }
    }

    /**
     * 通知服务配置
     */
    public static class Notification {

        private final Sms sms = new Sms();
        private final Email email = new Email();
        private final Push push = new Push();
        private final Wechat wechat = new Wechat();
        private final WechatWork wechatWork = new WechatWork();
        private final Template template = new Template();
        private final Center center = new Center();
        private final Preference preference = new Preference();
        private final Retry retry = new Retry();
        private final Batch batch = new Batch();

        public Sms getSms() {
            return sms;
        }

        public Email getEmail() {
            return email;
        }

        public Push getPush() {
            return push;
        }

        public Wechat getWechat() {
            return wechat;
        }

        public WechatWork getWechatWork() {
            return wechatWork;
        }

        public Template getTemplate() {
            return template;
        }

        public Center getCenter() {
            return center;
        }

        public Preference getPreference() {
            return preference;
        }

        public Retry getRetry() {
            return retry;
        }

        public Batch getBatch() {
            return batch;
        }

        /**
         * 短信服务配置
         */
        public static class Sms {

            private String accessKeyId;
            private String accessKeySecret;
            private String signName;
            private String endpoint = "dysmsapi.aliyuncs.com";
            private boolean enabled = false;
            private SmsProviderType defaultProvider = SmsProviderType.TENCENT;

            // 腾讯云短信配置
            private final TencentSms tencent = new TencentSms();

            public String getAccessKeyId() {
                return accessKeyId;
            }

            public void setAccessKeyId(String accessKeyId) {
                this.accessKeyId = accessKeyId;
            }

            public String getAccessKeySecret() {
                return accessKeySecret;
            }

            public void setAccessKeySecret(String accessKeySecret) {
                this.accessKeySecret = accessKeySecret;
            }

            public String getSignName() {
                return signName;
            }

            public void setSignName(String signName) {
                this.signName = signName;
            }

            public String getEndpoint() {
                return endpoint;
            }

            public void setEndpoint(String endpoint) {
                this.endpoint = endpoint;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public SmsProviderType getDefaultProvider() {
                return defaultProvider;
            }

            public void setDefaultProvider(SmsProviderType defaultProvider) {
                this.defaultProvider = defaultProvider;
            }

            public TencentSms getTencent() {
                return tencent;
            }

            /**
             * 腾讯云短信配置
             */
            public static class TencentSms {

                private String secretId;
                private String secretKey;
                private String appId;
                private String signName;
                private String region = "ap-guangzhou";
                private boolean enabled = false;

                public String getSecretId() {
                    return secretId;
                }

                public void setSecretId(String secretId) {
                    this.secretId = secretId;
                }

                public String getSecretKey() {
                    return secretKey;
                }

                public void setSecretKey(String secretKey) {
                    this.secretKey = secretKey;
                }

                public String getAppId() {
                    return appId;
                }

                public void setAppId(String appId) {
                    this.appId = appId;
                }

                public String getSignName() {
                    return signName;
                }

                public void setSignName(String signName) {
                    this.signName = signName;
                }

                public String getRegion() {
                    return region;
                }

                public void setRegion(String region) {
                    this.region = region;
                }

                public boolean isEnabled() {
                    return enabled;
                }

                public void setEnabled(boolean enabled) {
                    this.enabled = enabled;
                }
            }
        }

        /**
         * 邮件服务配置
         */
        public static class Email {

            private boolean enabled = false;
            private String from;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public String getFrom() {
                return from;
            }

            public void setFrom(String from) {
                this.from = from;
            }
        }

        /**
         * 推送服务配置（极光推送）
         */
        public static class Push {

            private String appKey;
            private String masterSecret;
            private boolean enabled = false;
            private boolean production = false; // 是否生产环境

            public String getAppKey() {
                return appKey;
            }

            public void setAppKey(String appKey) {
                this.appKey = appKey;
            }

            public String getMasterSecret() {
                return masterSecret;
            }

            public void setMasterSecret(String masterSecret) {
                this.masterSecret = masterSecret;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public boolean isProduction() {
                return production;
            }

            public void setProduction(boolean production) {
                this.production = production;
            }
        }

        /**
         * 微信公众号服务配置
         */
        public static class Wechat {

            private String appId;
            private String appSecret;
            private String token;
            private String aesKey;
            private boolean enabled = false;
            private String redirectUri;

            // 小程序配置
            private MiniApp miniApp = new MiniApp();

            public String getAppId() {
                return appId;
            }

            public void setAppId(String appId) {
                this.appId = appId;
            }

            public String getAppSecret() {
                return appSecret;
            }

            public void setAppSecret(String appSecret) {
                this.appSecret = appSecret;
            }

            public String getToken() {
                return token;
            }

            public void setToken(String token) {
                this.token = token;
            }

            public String getAesKey() {
                return aesKey;
            }

            public void setAesKey(String aesKey) {
                this.aesKey = aesKey;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public String getRedirectUri() {
                return redirectUri;
            }

            public void setRedirectUri(String redirectUri) {
                this.redirectUri = redirectUri;
            }

            public MiniApp getMiniApp() {
                return miniApp;
            }

            public void setMiniApp(MiniApp miniApp) {
                this.miniApp = miniApp;
            }

            /**
             * 微信小程序配置
             */
            public static class MiniApp {
                private String appId;
                private String appSecret;
                private boolean enabled = false;
                private String token;
                private String aesKey;

                public String getAppId() {
                    return appId;
                }

                public void setAppId(String appId) {
                    this.appId = appId;
                }

                public String getAppSecret() {
                    return appSecret;
                }

                public void setAppSecret(String appSecret) {
                    this.appSecret = appSecret;
                }

                public boolean isEnabled() {
                    return enabled;
                }

                public void setEnabled(boolean enabled) {
                    this.enabled = enabled;
                }

                public String getToken() {
                    return token;
                }

                public void setToken(String token) {
                    this.token = token;
                }

                public String getAesKey() {
                    return aesKey;
                }

                public void setAesKey(String aesKey) {
                    this.aesKey = aesKey;
                }
            }
        }

        /**
         * 企业微信服务配置
         */
        public static class WechatWork {

            private String corpId;
            private String corpSecret;
            private String agentId;
            private boolean enabled = false;
            private String token;
            private String aesKey;

            public String getCorpId() {
                return corpId;
            }

            public void setCorpId(String corpId) {
                this.corpId = corpId;
            }

            public String getCorpSecret() {
                return corpSecret;
            }

            public void setCorpSecret(String corpSecret) {
                this.corpSecret = corpSecret;
            }

            public String getAgentId() {
                return agentId;
            }

            public void setAgentId(String agentId) {
                this.agentId = agentId;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public String getToken() {
                return token;
            }

            public void setToken(String token) {
                this.token = token;
            }

            public String getAesKey() {
                return aesKey;
            }

            public void setAesKey(String aesKey) {
                this.aesKey = aesKey;
            }
        }

        /**
         * 模板配置
         */
        public static class Template {

            private boolean cacheEnabled = true;
            private int cacheTtlMinutes = 30;
            private String defaultLanguage = "zh-CN";

            public boolean isCacheEnabled() {
                return cacheEnabled;
            }

            public void setCacheEnabled(boolean cacheEnabled) {
                this.cacheEnabled = cacheEnabled;
            }

            public int getCacheTtlMinutes() {
                return cacheTtlMinutes;
            }

            public void setCacheTtlMinutes(int cacheTtlMinutes) {
                this.cacheTtlMinutes = cacheTtlMinutes;
            }

            public String getDefaultLanguage() {
                return defaultLanguage;
            }

            public void setDefaultLanguage(String defaultLanguage) {
                this.defaultLanguage = defaultLanguage;
            }
        }

        /**
         * 通知中心配置
         */
        public static class Center {

            private boolean enabled = true;
            private boolean asyncEnabled = true;
            private int defaultBatchSize = 100;
            private int maxBatchSize = 1000;

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public boolean isAsyncEnabled() {
                return asyncEnabled;
            }

            public void setAsyncEnabled(boolean asyncEnabled) {
                this.asyncEnabled = asyncEnabled;
            }

            public int getDefaultBatchSize() {
                return defaultBatchSize;
            }

            public void setDefaultBatchSize(int defaultBatchSize) {
                this.defaultBatchSize = defaultBatchSize;
            }

            public int getMaxBatchSize() {
                return maxBatchSize;
            }

            public void setMaxBatchSize(int maxBatchSize) {
                this.maxBatchSize = maxBatchSize;
            }
        }

        /**
         * 用户偏好设置配置
         */
        public static class Preference {

            private String defaultChannels = "PUSH, EMAIL, SMS";
            private boolean quietHoursEnabled = true;
            private String defaultQuietHoursStart = "22:00";
            private String defaultQuietHoursEnd = "08:00";

            public String getDefaultChannels() {
                return defaultChannels;
            }

            public void setDefaultChannels(String defaultChannels) {
                this.defaultChannels = defaultChannels;
            }

            public boolean isQuietHoursEnabled() {
                return quietHoursEnabled;
            }

            public void setQuietHoursEnabled(boolean quietHoursEnabled) {
                this.quietHoursEnabled = quietHoursEnabled;
            }

            public String getDefaultQuietHoursStart() {
                return defaultQuietHoursStart;
            }

            public void setDefaultQuietHoursStart(String defaultQuietHoursStart) {
                this.defaultQuietHoursStart = defaultQuietHoursStart;
            }

            public String getDefaultQuietHoursEnd() {
                return defaultQuietHoursEnd;
            }

            public void setDefaultQuietHoursEnd(String defaultQuietHoursEnd) {
                this.defaultQuietHoursEnd = defaultQuietHoursEnd;
            }
        }

        /**
         * 重试策略配置
         */
        public static class Retry {

            private int maxAttempts = 3;
            private int delaySeconds = 60;
            private double backoffMultiplier = 2.0;
            private int maxDelaySeconds = 3600;

            public int getMaxAttempts() {
                return maxAttempts;
            }

            public void setMaxAttempts(int maxAttempts) {
                this.maxAttempts = maxAttempts;
            }

            public int getDelaySeconds() {
                return delaySeconds;
            }

            public void setDelaySeconds(int delaySeconds) {
                this.delaySeconds = delaySeconds;
            }

            public double getBackoffMultiplier() {
                return backoffMultiplier;
            }

            public void setBackoffMultiplier(double backoffMultiplier) {
                this.backoffMultiplier = backoffMultiplier;
            }

            public int getMaxDelaySeconds() {
                return maxDelaySeconds;
            }

            public void setMaxDelaySeconds(int maxDelaySeconds) {
                this.maxDelaySeconds = maxDelaySeconds;
            }
        }

        /**
         * 批处理配置
         */
        public static class Batch {

            private int size = 100;
            private int threadPoolSize = 10;
            private int queueCapacity = 1000;

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public int getThreadPoolSize() {
                return threadPoolSize;
            }

            public void setThreadPoolSize(int threadPoolSize) {
                this.threadPoolSize = threadPoolSize;
            }

            public int getQueueCapacity() {
                return queueCapacity;
            }

            public void setQueueCapacity(int queueCapacity) {
                this.queueCapacity = queueCapacity;
            }
        }
    }

    /**
     * 电子签名服务配置
     */
    public static class ElectronicSignature {

        private String callbackBaseUrl;
        private final Fadada fadada = new Fadada();
        private final Esign esign = new Esign();

        public String getCallbackBaseUrl() {
            return callbackBaseUrl;
        }

        public void setCallbackBaseUrl(String callbackBaseUrl) {
            this.callbackBaseUrl = callbackBaseUrl;
        }

        public Fadada getFadada() {
            return fadada;
        }

        public Esign getEsign() {
            return esign;
        }

        /**
         * 法大大配置类
         */
        public static class Fadada {

            /**
             * 应用ID
             */
            private String appId;

            /**
             * 应用密钥
             */
            private String appSecret;

            /**
             * API地址
             */
            private String apiUrl;

            /**
             * 回调URL
             */
            private String callbackUrl;

            /**
             * 是否启用
             */
            private boolean enabled = true;

            public String getAppId() {
                return appId;
            }

            public void setAppId(String appId) {
                this.appId = appId;
            }

            public String getAppSecret() {
                return appSecret;
            }

            public void setAppSecret(String appSecret) {
                this.appSecret = appSecret;
            }

            public String getApiUrl() {
                return apiUrl;
            }

            public void setApiUrl(String apiUrl) {
                this.apiUrl = apiUrl;
            }

            public String getCallbackUrl() {
                return callbackUrl;
            }

            public void setCallbackUrl(String callbackUrl) {
                this.callbackUrl = callbackUrl;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }
        }

        /**
         * e签宝配置类
         */
        public static class Esign {

            /**
             * 应用ID
             */
            private String appId;

            /**
             * 应用密钥
             */
            private String appSecret;

            /**
             * API地址
             */
            private String apiUrl;

            /**
             * 回调URL
             */
            private String callbackUrl;

            /**
             * 是否启用
             */
            private boolean enabled = true;

            public String getAppId() {
                return appId;
            }

            public void setAppId(String appId) {
                this.appId = appId;
            }

            public String getAppSecret() {
                return appSecret;
            }

            public void setAppSecret(String appSecret) {
                this.appSecret = appSecret;
            }

            public String getApiUrl() {
                return apiUrl;
            }

            public void setApiUrl(String apiUrl) {
                this.apiUrl = apiUrl;
            }

            public String getCallbackUrl() {
                return callbackUrl;
            }

            public void setCallbackUrl(String callbackUrl) {
                this.callbackUrl = callbackUrl;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }
        }
    }

    /**
     * 验证码服务配置
     */
    @Data
    public static class VerificationCode {

        /**
         * 是否启用验证码服务
         */
        private boolean enabled = true;

        /**
         * 验证码长度
         */
        private int codeLength = 4;

        /**
         * 验证码有效期（分钟）
         */
        private int expirationMinutes = 5;

        /**
         * 发送间隔（秒）
         */
        private int sendIntervalSeconds = 60;
    }
}
