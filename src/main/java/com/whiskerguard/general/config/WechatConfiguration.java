package com.whiskerguard.general.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信公众号配置类
 */
@Configuration
@EnableConfigurationProperties(ApplicationProperties.class)
@ConditionalOnProperty(prefix = "application.notification.wechat", name = "enabled", havingValue = "true")
public class WechatConfiguration {

    private static final Logger log = LoggerFactory.getLogger(WechatConfiguration.class);

    private final ApplicationProperties applicationProperties;

    public WechatConfiguration(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    /**
     * 微信公众号服务Bean
     */
    @Bean
    public WxMpService wxMpService() {
        ApplicationProperties.Notification.Wechat wechatProperties =
            applicationProperties.getNotification().getWechat();

        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(wechatProperties.getAppId());
        config.setSecret(wechatProperties.getAppSecret());
        config.setToken(wechatProperties.getToken());

        if (wechatProperties.getAesKey() != null && !wechatProperties.getAesKey().isEmpty()) {
            config.setAesKey(wechatProperties.getAesKey());
        }

        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);

        log.info("微信公众号服务配置完成，AppId: {}", wechatProperties.getAppId());
        return wxMpService;
    }

    /**
     * 微信小程序服务Bean
     */
    @Bean
    @ConditionalOnProperty(prefix = "application.notification.wechat.mini-app", name = "enabled", havingValue = "true")
    public WxMaService wxMaService() {
        ApplicationProperties.Notification.Wechat.MiniApp miniAppProperties =
            applicationProperties.getNotification().getWechat().getMiniApp();

        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(miniAppProperties.getAppId());
        config.setSecret(miniAppProperties.getAppSecret());
        config.setToken(miniAppProperties.getToken());

        if (miniAppProperties.getAesKey() != null && !miniAppProperties.getAesKey().isEmpty()) {
            config.setAesKey(miniAppProperties.getAesKey());
        }

        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);

        log.info("微信小程序服务配置完成，AppId: {}", miniAppProperties.getAppId());
        return wxMaService;
    }
}
