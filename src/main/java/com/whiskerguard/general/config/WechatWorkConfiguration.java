package com.whiskerguard.general.config;

import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 企业微信配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
@Configuration
@ConditionalOnProperty(prefix = "application.notification.wechat-work", name = "enabled", havingValue = "true")
public class WechatWorkConfiguration {

    private static final Logger log = LoggerFactory.getLogger(WechatWorkConfiguration.class);

    private final ApplicationProperties applicationProperties;

    public WechatWorkConfiguration(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    /**
     * 企业微信服务Bean
     */
    @Bean
    public WxCpService wxCpService() {
        ApplicationProperties.Notification.WechatWork wechatWorkProperties =
            applicationProperties.getNotification().getWechatWork();

        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        config.setCorpId(wechatWorkProperties.getCorpId());
        config.setCorpSecret(wechatWorkProperties.getCorpSecret());
        config.setAgentId(Integer.valueOf(wechatWorkProperties.getAgentId()));
        
        if (wechatWorkProperties.getToken() != null && !wechatWorkProperties.getToken().isEmpty()) {
            config.setToken(wechatWorkProperties.getToken());
        }
        
        if (wechatWorkProperties.getAesKey() != null && !wechatWorkProperties.getAesKey().isEmpty()) {
            config.setAesKey(wechatWorkProperties.getAesKey());
        }

        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(config);

        log.info("企业微信服务Bean初始化成功，CorpId: {}, AgentId: {}", 
                wechatWorkProperties.getCorpId(), wechatWorkProperties.getAgentId());

        return wxCpService;
    }
}
