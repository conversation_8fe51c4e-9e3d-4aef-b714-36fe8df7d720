package com.whiskerguard.general.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 微信小程序消息模板枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
public enum WechatMiniAppTemplateEnum {

    SYSTEM_UPGRADE("系统升级通知",
        Arrays.asList("开始时间", "结束时间", "备注", "发布时间"),
        "qy3Yfu_7PbhXjPZ0dS-E0M-kaZhCRQhu8k_Lf7pZkPE"),

    BUSINESS_ANNOUNCEMENT("企业新公告提醒",
        Arrays.asList("公告标题", "公告内容", "通知时间", "发文时间"),
        "ykHuqswVmyYa3tr7i6kLzRiOBt0PRqBgOwSJUpLa33Y"),

    TODO_REMINDER("待办事项通知",
        Arrays.asList("待办事项", "待办内容", "通知时间", "备注"),
        "pr655Pz-emamDxrAgd8iUwVQRgkwOL0AjcC2wLY91jQ"),

    APPROVAL_RESULT("审核结果通知",
        Arrays.asList("通知时间", "通知内容", "审批时间", "审核结果", "备注"),
        "XlJqAJ8g96_NZiqCwMotRuIwb7J3w4KigB37BpAuQVU");

    /**
     * 模板标题
     */
    private final String title;

    /**
     * 模板关键词列表
     */
    private final List<String> keywords;

    /**
     * 模板ID
     */
    private final String templateId;

    /**
     * 构造函数
     *
     * @param title      模板标题
     * @param keywords   关键词列表
     * @param templateId 模板ID
     */
    WechatMiniAppTemplateEnum(String title, List<String> keywords, String templateId) {
        this.title = title;
        this.keywords = keywords;
        this.templateId = templateId;
    }

    /**
     * 获取模板标题
     *
     * @return 模板标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 获取关键词列表
     *
     * @return 关键词列表
     */
    public List<String> getKeywords() {
        return keywords;
    }

    /**
     * 获取模板ID
     *
     * @return 模板ID
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * 根据模板ID查找枚举
     *
     * @param templateId 模板ID
     * @return 对应的枚举值，如果未找到返回null
     */
    public static WechatMiniAppTemplateEnum findByTemplateId(String templateId) {
        if (templateId == null || templateId.trim().isEmpty()) {
            return null;
        }

        for (WechatMiniAppTemplateEnum template : values()) {
            if (template.getTemplateId().equals(templateId)) {
                return template;
            }
        }
        return null;
    }

    /**
     * 根据标题查找枚举
     *
     * @param title 模板标题
     * @return 对应的枚举值，如果未找到返回null
     */
    public static WechatMiniAppTemplateEnum findByTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return null;
        }

        for (WechatMiniAppTemplateEnum template : values()) {
            if (template.getTitle().equals(title)) {
                return template;
            }
        }
        return null;
    }

    /**
     * 检查关键词是否匹配
     *
     * @param keyword 要检查的关键词
     * @return 是否包含该关键词
     */
    public boolean containsKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return false;
        }
        return keywords.contains(keyword);
    }

    /**
     * 获取模板信息的字符串表示
     *
     * @return 格式化的模板信息
     */
    @Override
    public String toString() {
        return String.format("WechatMiniAppTemplate{title='%s', templateId='%s', keywords=%s}",
            title, templateId, keywords);
    }

    /**
     * 获取模板的详细描述
     *
     * @return 模板详细描述
     */
    public String getDescription() {
        return String.format("模板标题: %s\n模板ID: %s\n关键词: %s",
            title, templateId, String.join(", ", keywords));
    }
}
