package com.whiskerguard.general.model;

import java.time.Instant;

/**
 * 通知响应
 */
public class NotificationResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 通知类型
     */
    private NotificationType type;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 时间戳
     */
    private Instant timestamp;

    public NotificationResponse() {}

    public NotificationResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public static NotificationResponse success(String message) {
        return new NotificationResponse(true, message);
    }

    public static NotificationResponse success(String message, String businessId) {
        NotificationResponse response = new NotificationResponse(true, message);
        response.setBusinessId(businessId);
        return response;
    }

    public static NotificationResponse failure(String message) {
        return new NotificationResponse(false, message);
    }

    public static NotificationResponse failure(String message, String code) {
        NotificationResponse response = new NotificationResponse(false, message);
        response.setCode(code);
        return response;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }

    // 链式调用方法
    public NotificationResponse messageId(String messageId) {
        this.messageId = messageId;
        return this;
    }

    public NotificationResponse errorCode(String errorCode) {
        this.errorCode = errorCode;
        return this;
    }

    public NotificationResponse timestamp(Instant timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public NotificationResponse businessId(String businessId) {
        this.businessId = businessId;
        return this;
    }

    public NotificationResponse type(NotificationType type) {
        this.type = type;
        return this;
    }

    public NotificationResponse code(String code) {
        this.code = code;
        return this;
    }
}
