package com.whiskerguard.general.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 企业微信推送请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
public class WechatWorkRequest {

    /**
     * 消息类型：text, image, voice, video, file, textcard, news, mpnews, markdown
     */
    @NotBlank(message = "消息类型不能为空")
    @JsonProperty("msgtype")
    private String msgType;

    /**
     * 接收消息的用户ID列表（企业微信用户ID）
     */
    @JsonProperty("touser")
    private String toUser;

    /**
     * 接收消息的部门ID列表
     */
    @JsonProperty("toparty")
    private String toParty;

    /**
     * 接收消息的标签ID列表
     */
    @JsonProperty("totag")
    private String toTag;

    /**
     * 企业应用的id，整型。企业内部开发，可在应用的设置页面查看
     */
    @NotNull(message = "应用ID不能为空")
    @JsonProperty("agentid")
    private Integer agentId;

    /**
     * 表示是否是保密消息，0表示否，1表示是，默认0
     */
    @JsonProperty("safe")
    private Integer safe = 0;

    /**
     * 表示是否开启id转译，0表示否，1表示是，默认0
     */
    @JsonProperty("enable_id_trans")
    private Integer enableIdTrans = 0;

    /**
     * 表示是否开启重复消息检查，0表示否，1表示是，默认0
     */
    @JsonProperty("enable_duplicate_check")
    private Integer enableDuplicateCheck = 0;

    /**
     * 表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
     */
    @JsonProperty("duplicate_check_interval")
    private Integer duplicateCheckInterval = 1800;

    /**
     * 文本消息内容
     */
    @JsonProperty("text")
    private TextMessage text;

    /**
     * 图片消息内容
     */
    @JsonProperty("image")
    private MediaMessage image;

    /**
     * 语音消息内容
     */
    @JsonProperty("voice")
    private MediaMessage voice;

    /**
     * 视频消息内容
     */
    @JsonProperty("video")
    private VideoMessage video;

    /**
     * 文件消息内容
     */
    @JsonProperty("file")
    private MediaMessage file;

    /**
     * 文本卡片消息内容
     */
    @JsonProperty("textcard")
    private TextCardMessage textCard;

    /**
     * 图文消息内容
     */
    @JsonProperty("news")
    private NewsMessage news;

    /**
     * 图文消息（mpnews）内容
     */
    @JsonProperty("mpnews")
    private MpNewsMessage mpNews;

    /**
     * Markdown消息内容
     */
    @JsonProperty("markdown")
    private MarkdownMessage markdown;

    // 内部类定义各种消息类型

    /**
     * 文本消息
     */
    public static class TextMessage {
        @JsonProperty("content")
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    /**
     * 媒体消息（图片、语音、文件）
     */
    public static class MediaMessage {
        @JsonProperty("media_id")
        private String mediaId;

        public String getMediaId() {
            return mediaId;
        }

        public void setMediaId(String mediaId) {
            this.mediaId = mediaId;
        }
    }

    /**
     * 视频消息
     */
    public static class VideoMessage {
        @JsonProperty("media_id")
        private String mediaId;

        @JsonProperty("title")
        private String title;

        @JsonProperty("description")
        private String description;

        public String getMediaId() {
            return mediaId;
        }

        public void setMediaId(String mediaId) {
            this.mediaId = mediaId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    /**
     * 文本卡片消息
     */
    public static class TextCardMessage {
        @JsonProperty("title")
        private String title;

        @JsonProperty("description")
        private String description;

        @JsonProperty("url")
        private String url;

        @JsonProperty("btntxt")
        private String btnTxt;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getBtnTxt() {
            return btnTxt;
        }

        public void setBtnTxt(String btnTxt) {
            this.btnTxt = btnTxt;
        }
    }

    /**
     * 图文消息
     */
    public static class NewsMessage {
        @JsonProperty("articles")
        private List<Article> articles;

        public List<Article> getArticles() {
            return articles;
        }

        public void setArticles(List<Article> articles) {
            this.articles = articles;
        }

        public static class Article {
            @JsonProperty("title")
            private String title;

            @JsonProperty("description")
            private String description;

            @JsonProperty("url")
            private String url;

            @JsonProperty("picurl")
            private String picUrl;

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getUrl() {
                return url;
            }

            public void setUrl(String url) {
                this.url = url;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }
        }
    }

    /**
     * 图文消息（mpnews）
     */
    public static class MpNewsMessage {
        @JsonProperty("articles")
        private List<MpArticle> articles;

        public List<MpArticle> getArticles() {
            return articles;
        }

        public void setArticles(List<MpArticle> articles) {
            this.articles = articles;
        }

        public static class MpArticle {
            @JsonProperty("title")
            private String title;

            @JsonProperty("thumb_media_id")
            private String thumbMediaId;

            @JsonProperty("author")
            private String author;

            @JsonProperty("content_source_url")
            private String contentSourceUrl;

            @JsonProperty("content")
            private String content;

            @JsonProperty("digest")
            private String digest;

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getThumbMediaId() {
                return thumbMediaId;
            }

            public void setThumbMediaId(String thumbMediaId) {
                this.thumbMediaId = thumbMediaId;
            }

            public String getAuthor() {
                return author;
            }

            public void setAuthor(String author) {
                this.author = author;
            }

            public String getContentSourceUrl() {
                return contentSourceUrl;
            }

            public void setContentSourceUrl(String contentSourceUrl) {
                this.contentSourceUrl = contentSourceUrl;
            }

            public String getContent() {
                return content;
            }

            public void setContent(String content) {
                this.content = content;
            }

            public String getDigest() {
                return digest;
            }

            public void setDigest(String digest) {
                this.digest = digest;
            }
        }
    }

    /**
     * Markdown消息
     */
    public static class MarkdownMessage {
        @JsonProperty("content")
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    // Getters and Setters

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getToUser() {
        return toUser;
    }

    public void setToUser(String toUser) {
        this.toUser = toUser;
    }

    public String getToParty() {
        return toParty;
    }

    public void setToParty(String toParty) {
        this.toParty = toParty;
    }

    public String getToTag() {
        return toTag;
    }

    public void setToTag(String toTag) {
        this.toTag = toTag;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public Integer getSafe() {
        return safe;
    }

    public void setSafe(Integer safe) {
        this.safe = safe;
    }

    public Integer getEnableIdTrans() {
        return enableIdTrans;
    }

    public void setEnableIdTrans(Integer enableIdTrans) {
        this.enableIdTrans = enableIdTrans;
    }

    public Integer getEnableDuplicateCheck() {
        return enableDuplicateCheck;
    }

    public void setEnableDuplicateCheck(Integer enableDuplicateCheck) {
        this.enableDuplicateCheck = enableDuplicateCheck;
    }

    public Integer getDuplicateCheckInterval() {
        return duplicateCheckInterval;
    }

    public void setDuplicateCheckInterval(Integer duplicateCheckInterval) {
        this.duplicateCheckInterval = duplicateCheckInterval;
    }

    public TextMessage getText() {
        return text;
    }

    public void setText(TextMessage text) {
        this.text = text;
    }

    public MediaMessage getImage() {
        return image;
    }

    public void setImage(MediaMessage image) {
        this.image = image;
    }

    public MediaMessage getVoice() {
        return voice;
    }

    public void setVoice(MediaMessage voice) {
        this.voice = voice;
    }

    public VideoMessage getVideo() {
        return video;
    }

    public void setVideo(VideoMessage video) {
        this.video = video;
    }

    public MediaMessage getFile() {
        return file;
    }

    public void setFile(MediaMessage file) {
        this.file = file;
    }

    public TextCardMessage getTextCard() {
        return textCard;
    }

    public void setTextCard(TextCardMessage textCard) {
        this.textCard = textCard;
    }

    public NewsMessage getNews() {
        return news;
    }

    public void setNews(NewsMessage news) {
        this.news = news;
    }

    public MpNewsMessage getMpNews() {
        return mpNews;
    }

    public void setMpNews(MpNewsMessage mpNews) {
        this.mpNews = mpNews;
    }

    public MarkdownMessage getMarkdown() {
        return markdown;
    }

    public void setMarkdown(MarkdownMessage markdown) {
        this.markdown = markdown;
    }
}
