package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Spring Data JPA repository for the NotificationRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface NotificationRecordRepository extends JpaRepository<NotificationRecord, Long> {
    /**
     * 根据业务ID和业务类型查找通知记录
     */
    List<NotificationRecord> findByBusinessIdAndBusinessType(String businessId, String businessType);

    /**
     * 查找指定状态且计划发送时间小于等于指定时间的通知记录
     */
    List<NotificationRecord> findByStatusAndScheduledTimeLessThanEqual(NotificationStatus status, Instant scheduledTime);

    /**
     * 查找指定状态且计划发送时间小于等于指定时间的通知记录（别名方法）
     */
    default List<NotificationRecord> findByStatusAndScheduledTimeBeforeOrEqual(NotificationStatus status, Instant scheduledTime) {
        return findByStatusAndScheduledTimeLessThanEqual(status, scheduledTime);
    }

    /**
     * 根据租户ID和通知类别查找通知
     */
    @Query("SELECT n FROM NotificationRecord n WHERE n.tenantId = ?1 AND n.category = ?2 AND n.isDeleted = false")
    List<NotificationRecord> findByTenantIdAndCategory(Long tenantId, NotificationCategory category);

    /**
     * 分页查询指定租户ID和通知类别的通知记录
     */
    @Query("SELECT n FROM NotificationRecord n WHERE n.tenantId = ?1 AND n.category = ?2 AND n.isDeleted = false")
    Page<NotificationRecord> pageByTenantIdAndCategory(Long tenantId, NotificationCategory category, Pageable pageable);

    /**
     * 分页查询指定通知类别的通知记录
     */
    @Query("SELECT n FROM NotificationRecord n WHERE n.category = ?1 AND n.isDeleted = false")
    Page<NotificationRecord> pageByCategory(NotificationCategory category, Pageable pageable);
}
