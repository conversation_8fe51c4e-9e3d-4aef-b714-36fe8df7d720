package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the NotificationSendRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface NotificationSendRecordRepository extends JpaRepository<NotificationSendRecord, Long> {
    List<NotificationSendRecord> findByNotificationIdAndRecipientIdAndRecipientType(
        Long notificationId,
        Long recipientId,
        RecipientType recipientType
    );

    List<NotificationSendRecord> findByNotificationId(Long notificationId);

    List<NotificationSendRecord> findByNotificationIdInAndRecipientIdAndRecipientType(
        List<Long> notificationIds,
        Long recipientId,
        RecipientType recipientType
    );

    /**
     * 根据通知分类查询发送记录
     */
    @Query("SELECT nsr FROM NotificationSendRecord nsr JOIN nsr.notification n WHERE n.category = :category AND nsr.isDeleted = false")
    Page<NotificationSendRecord> findByCategory(NotificationCategory category, Pageable pageable);

    /**
     * 根据接收者ID和接收者类型查询发送记录
     */
    @Query(
        "SELECT nsr FROM NotificationSendRecord nsr WHERE nsr.recipientId = :recipientId AND nsr.recipientType = :recipientType AND nsr.isDeleted = false"
    )
    Page<NotificationSendRecord> findByRecipientIdAndRecipientType(Long recipientId, RecipientType recipientType, Pageable pageable);

    /**
     * 根据接收者ID和通知ID列表查询发送记录
     */
    @Query("SELECT nsr FROM NotificationSendRecord nsr WHERE nsr.recipientId = :userId AND nsr.notification.id IN :notificationIds AND nsr.isDeleted = false" +
        " AND nsr.status = 'SENT'")
    Page<NotificationSendRecord> findByRecipientIdAndNotificationIdIn(Long userId, List<Long> notificationIds, Pageable pageable);

    /**
     * 统计用户未读消息数量
     */
    @Query("SELECT COUNT(nsr) FROM NotificationSendRecord nsr WHERE nsr.recipientId = :userId AND nsr.recipientType = :recipientType " +
        "AND nsr.readTime IS NULL AND nsr.status = 'SENT' AND nsr.isDeleted = false")
    Long countUnreadByRecipientIdAndRecipientType(Long userId, RecipientType recipientType);

    /**
     * 统计用户未读消息数量（按分类）
     */
    @Query("SELECT COUNT(nsr) FROM NotificationSendRecord nsr JOIN nsr.notification n WHERE nsr.recipientId = :userId " +
        "AND nsr.recipientType = :recipientType AND n.category = :category AND nsr.readTime IS NULL " +
        "AND nsr.status = 'SENT' AND nsr.isDeleted = false")
    Long countUnreadByRecipientIdAndRecipientTypeAndCategory(Long userId, RecipientType recipientType, NotificationCategory category);

}
