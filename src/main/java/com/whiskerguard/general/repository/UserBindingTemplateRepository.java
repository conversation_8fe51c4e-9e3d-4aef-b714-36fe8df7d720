package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.UserBindingTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户绑定消息模板Repository接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
@SuppressWarnings("unused")
@Repository
public interface UserBindingTemplateRepository extends JpaRepository<UserBindingTemplate, Long> {

    /**
     * 根据用户ID查找绑定的消息模板
     *
     * @param userId 用户ID
     * @return 绑定的消息模板列表
     * @since 1.0
     */
    @Query("SELECT u FROM UserBindingTemplate u WHERE u.isDeleted = false AND u.employeeId = :userId ORDER BY u.createdAt DESC")
    List<UserBindingTemplate> findByEmployeeId(Long userId);

    /**
     * 根据用户ID和模板ID查找绑定的消息模板
     *
     * @param userId     用户ID
     * @param templateId 模板ID
     * @return 绑定的消息模板
     * @since 1.0
     */
    @Query("SELECT u FROM UserBindingTemplate u WHERE u.isDeleted = false AND u.employeeId = :userId AND u.templateId = :templateId")
    UserBindingTemplate findByEmployeeIdAndTemplateId(Long userId, String templateId);
}
