package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.UserWechatBinding;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户微信绑定Repository接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
@Repository
public interface UserWechatBindingRepository extends JpaRepository<UserWechatBinding, Long>, JpaSpecificationExecutor<UserWechatBinding> {

    /**
     * 根据用户ID查找绑定记录
     *
     * @param employeeId 用户ID
     * @return 绑定记录列表
     * @since 1.0
     */
    List<UserWechatBinding> findByEmployeeIdAndIsDeletedFalse(Long employeeId);

    /**
     * 根据用户ID和OpenID查找绑定记录
     *
     * @param employeeId 用户ID
     * @param openId     微信OpenID
     * @return 绑定记录
     * @since 1.0
     */
    Optional<UserWechatBinding> findByEmployeeIdAndOpenIdAndIsDeletedFalse(Long employeeId, String openId);
    
    /**
     * 分页查找所有绑定记录
     *
     * @param currentTenantId 当前租户ID
     * @param pageable        分页信息
     * @return 绑定记录分页数据
     * @since 1.0
     */
    @Query("SELECT u FROM UserWechatBinding u WHERE u.tenantId = :currentTenantId AND u.isDeleted = false")
    Page<UserWechatBinding> findAll(Long currentTenantId, Pageable pageable);

    /**
     * 根据用户ID和OpenID查找绑定记录
     *
     * @param employeeId 用户ID
     * @param openId     微信OpenID
     * @return 绑定记录
     * @since 1.0
     */
    @Query("SELECT u FROM UserWechatBinding u WHERE u.isDeleted = false AND u.employeeId = :employeeId AND u.openId = :openId")
    UserWechatBinding findOneByOpenIdAndEmployeeId(Long employeeId, String openId);

    /**
     * 根据OpenID更新绑定记录的删除状态
     *
     * @param openId 微信OpenID
     * @since 1.0
     */
    @Modifying
    @Query("UPDATE UserWechatBinding u SET u.isDeleted = true WHERE u.openId = :openId")
    void updateIsDeletedByOpenId(String openId);


}
