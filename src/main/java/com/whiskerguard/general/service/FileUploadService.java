package com.whiskerguard.general.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 描述：文件上传的服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
public interface FileUploadService {
    /**
     * 处理文件上传请求的方法。
     *
     * @param file         要上传的文件，通过请求参数 "file" 传递。
     * @param categoryName 以"-"组成的类别
     * @param serviceName  服务名称，服务名称，用以生成buket。
     * @return 如果文件上传成功，返回包含文件URL的200 OK响应；
     * 如果文件为空，返回400 Bad Request响应；
     * 如果文件类型不被允许，返回400 Bad Request响应；
     * 如果上传过程中发生异常，返回500 Internal Server Error响应。
     * @throws Exception 当参数不合法时抛出此异常
     */
    Map<String, Object> uploadFile(MultipartFile file, String categoryName, String serviceName) throws Exception;

    /**
     * 获取指定目录下的所有文件列表
     *
     * @param tenantId     租户ID，用于区分不同租户的文件
     * @param categoryName 文件类别名称，以"-"分隔的类别路径
     * @param uploadTime   上传时间，用于筛选特定时间段的文件
     * @param serviceName  服务名称，服务名称，用以生成buket。
     * @return 返回文件路径列表，如果目录为空或不存在则返回空列表
     */
    List<String> getFilesInDirectory(Long tenantId, String categoryName, String uploadTime, String serviceName);

    /**
     * 获取指定文件在COS中的URL。
     *
     * @param key 文件的Key
     * @return 包含文件URL的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    String getFileUrl(String key);

    /**
     * 批量上传文件
     *
     * @param files        要上传的文件列表
     * @param categoryName 以"-"组成的类别
     * @param serviceName  服务名称，用以生成bucket
     * @return 批量上传结果，包含成功和失败的文件信息
     * @throws Exception 当参数不合法时抛出此异常
     */
    Map<String, Object> batchUploadFiles(List<MultipartFile> files, String categoryName, String serviceName) throws Exception;

    /**
     * 异步批量上传文件
     *
     * @param files        要上传的文件列表
     * @param categoryName 以"-"组成的类别
     * @param serviceName  服务名称，用以生成bucket
     * @return 异步批量上传结果
     */
    CompletableFuture<Map<String, Object>> batchUploadFilesAsync(List<MultipartFile> files, String categoryName, String serviceName);

    /**
     * 验证文件类型
     *
     * @param contentType 文件内容类型
     * @return 是否为允许的文件类型
     */
    boolean isAllowedFileType(String contentType);

    /**
     * 获取文件上传进度（用于大文件上传）
     *
     * @param uploadId 上传任务ID
     * @return 上传进度信息
     */
    Map<String, Object> getUploadProgress(String uploadId);

}
