package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.service.dto.BatchNotificationRequestDTO;
import com.whiskerguard.general.service.dto.BatchNotificationStatusDTO;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.dto.NotificationRequestDTO;
import com.whiskerguard.general.service.dto.SystemNotificationRequestDTO;
import com.whiskerguard.general.service.dto.TaskNotificationRequestDTO;
import com.whiskerguard.general.service.dto.UserNotificationRequestDTO;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 通知中心服务接口
 *
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
public interface NotificationCenterService {
    /**
     * 发送单个通知
     *
     * @param request 通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendNotification(NotificationRequestDTO request);

    /**
     * 批量发送通知
     *
     * @param request 批量通知请求
     * @return 批次ID
     */
    String sendBatchNotification(BatchNotificationRequestDTO request);

    /**
     * 发送系统通知
     *
     * @param request 系统通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendSystemNotification(SystemNotificationRequestDTO request);

    /**
     * 发送任务通知
     *
     * @param request 任务通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendTaskNotification(TaskNotificationRequestDTO request);

    /**
     * 发送用户通知
     *
     * @param request 用户通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request);

    /**
     * 重新发送失败的通知
     */
    void retryFailedNotifications();

    /**
     * 取消计划中的通知
     *
     * @param notificationId 通知ID
     */
    void cancelScheduledNotification(Long notificationId);

    /**
     * 获取批量通知状态
     *
     * @param batchId 批次ID
     * @return 批量通知状态
     */
    BatchNotificationStatusDTO getBatchNotificationStatus(String batchId);

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     */
    void markAsRead(Long notificationId, Long userId);

    /**
     * 批量标记通知为已读
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     */
    void batchMarkAsRead(List<Long> notificationIds, Long userId);

    /**
     * 根据通知ID获取通知详情
     *
     * @param id 通知ID
     * @return 通知详情
     */
    Optional<NotificationRecordDTO> findNotificationRecord(Long id);

    /**
     * 统计用户未读消息数量
     *
     * @param userId 用户ID
     * @param recipientType 接收者类型
     * @return 未读消息数量
     */
    Long countUnreadMessages(Long userId, RecipientType recipientType);

    /**
     * 统计用户未读消息数量（按分类）
     *
     * @param userId 用户ID
     * @param recipientType 接收者类型
     * @return 按分类统计的未读消息数量
     */
    Map<NotificationCategory, Long> countUnreadMessagesByCategory(Long userId, RecipientType recipientType);
}
