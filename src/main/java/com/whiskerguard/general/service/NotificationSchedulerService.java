package com.whiskerguard.general.service;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.UserBindingTemplate;
import com.whiskerguard.general.domain.UserWechatBinding;
import com.whiskerguard.general.domain.enumeration.*;
import com.whiskerguard.general.enums.WechatMiniAppTemplateEnum;
import com.whiskerguard.general.model.*;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.repository.UserBindingTemplateRepository;
import com.whiskerguard.general.repository.UserWechatBindingRepository;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import com.whiskerguard.general.service.dto.SubscribeMessageRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通知调度服务 - 处理定时发送通知
 */
@Service
@EnableScheduling
public class NotificationSchedulerService {

    private final Logger log = LoggerFactory.getLogger(NotificationSchedulerService.class);

    private final NotificationRecordRepository notificationRecordRepository;
    private final NotificationService notificationService;
    private final NotificationSendRecordService notificationSendRecordService;
    private final ObjectMapper objectMapper;
    private final WechatMiniAppService wechatMiniAppService;
    private final UserWechatBindingRepository userWechatBindingRepository;
    private final UserBindingTemplateRepository userBindingTemplateRepository;


    @Autowired
    public NotificationSchedulerService(
        NotificationRecordRepository notificationRecordRepository,
        NotificationService notificationService,
        NotificationSendRecordService notificationSendRecordService,
        ObjectMapper objectMapper,
        WechatMiniAppService wechatMiniAppService,
        UserWechatBindingRepository userWechatBindingRepository,
        UserBindingTemplateRepository userBindingTemplateRepository
    ) {
        this.notificationRecordRepository = notificationRecordRepository;
        this.notificationService = notificationService;
        this.notificationSendRecordService = notificationSendRecordService;
        this.objectMapper = objectMapper;
        this.wechatMiniAppService = wechatMiniAppService;
        this.userWechatBindingRepository = userWechatBindingRepository;
        this.userBindingTemplateRepository = userBindingTemplateRepository;
    }

    /**
     * 每分钟执行一次，检查并发送计划发送的通知
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 * * * * *")
    @Transactional
    public void processScheduledNotifications() {
        log.debug("定时任务开始执行 - 处理计划发送的通知");

        // 查找当前时间应发送的通知（状态为SCHEDULED且scheduledTime <= now）
        Instant now = Instant.now();
        List<NotificationRecord> pendingNotifications = notificationRecordRepository.findByStatusAndScheduledTimeBeforeOrEqual(
            NotificationStatus.SCHEDULED, now);

        log.debug("找到 {} 条待发送通知", pendingNotifications.size());

        for (NotificationRecord notification : pendingNotifications) {
            try {
                // 更新通知状态为处理中
                notification.setStatus(NotificationStatus.PROCESSING);
                notificationRecordRepository.save(notification);

                // 处理通知发送
                processNotification(notification);

                // 更新通知状态为已发送
                notification.setStatus(NotificationStatus.SENT);
                notification.setSentTime(Instant.now());
                notificationRecordRepository.save(notification);
            } catch (Exception e) {
                log.error("发送通知失败, 通知ID: {}, 错误: {}", notification.getId(), e.getMessage(), e);

                // 更新重试次数和错误信息
                int retryCount = notification.getRetryCount() != null ? notification.getRetryCount() + 1 : 1;
                notification.setRetryCount(retryCount);
                notification.setErrorMessage(e.getMessage());

                // 如果重试次数超过限制，标记为失败
                if (retryCount >= 3) {
                    notification.setStatus(NotificationStatus.FAILED);
                } else {
                    notification.setStatus(NotificationStatus.SCHEDULED);
                }

                notificationRecordRepository.save(notification);
            }
        }
    }

    /**
     * 处理单个通知的发送
     */
    private void processNotification(NotificationRecord notification) throws JsonProcessingException {
        // 解析接收者ID列表
        String[] recipientIds = parseRecipientIds(notification.getRecipientIds());
        // 解析发送渠道
        List<String> channels = parseChannels(notification.getChannels());

        // 对每个接收者通过每个渠道发送通知
        for (String recipientId : recipientIds) {
            for (String channel : channels) {
                NotificationResponse response = sendNotificationByChannel(notification, recipientId, channel);

                // 创建发送记录
                NotificationSendRecordDTO sendRecord = createSendRecord(notification, recipientId, channel, response);
                notificationSendRecordService.save(sendRecord);
            }
        }
    }

    /**
     * 解析接收者ID列表
     */
    private String[] parseRecipientIds(String recipientIdsJson) throws JsonProcessingException {
        if (recipientIdsJson == null || recipientIdsJson.trim().isEmpty()) {
            return new String[0];
        }

        try {
            // 首先尝试解析为Long数组，然后转换为String数组
            Long[] longIds = objectMapper.readValue(recipientIdsJson, Long[].class);
            return Arrays.stream(longIds).map(String::valueOf).toArray(String[]::new);
        } catch (JsonProcessingException e) {
            log.debug("尝试解析为Long数组失败，尝试解析为String数组: {}", e.getMessage());
            // 如果解析Long数组失败，尝试解析为String数组
            return objectMapper.readValue(recipientIdsJson, String[].class);
        }
    }

    /**
     * 解析发送渠道列表
     */
    private List<String> parseChannels(String channelsJson) throws JsonProcessingException {
        if (channelsJson == null || channelsJson.trim().isEmpty()) {
            return List.of("SMS"); // 默认短信渠道
        }

        try {
            // 首先尝试解析为枚举数组
            com.whiskerguard.general.domain.enumeration.NotificationType[] enumChannels = objectMapper.readValue(
                channelsJson,
                com.whiskerguard.general.domain.enumeration.NotificationType[].class
            );
            return Arrays.stream(enumChannels).map(Enum::name).collect(java.util.stream.Collectors.toList());
        } catch (JsonProcessingException e) {
            log.debug("尝试解析为枚举数组失败，尝试解析为String数组: {}", e.getMessage());
            // 如果解析枚举数组失败，尝试解析为String数组
            String[] stringChannels = objectMapper.readValue(channelsJson, String[].class);
            return Arrays.asList(stringChannels);
        }
    }

    /**
     * 根据渠道类型发送通知
     */
    private NotificationResponse sendNotificationByChannel(NotificationRecord notification, String recipientId, String channel) {
        // 根据不同渠道发送通知
        return switch (channel.toUpperCase()) {
            case "SMS" -> {
                SmsRequest smsRequest = buildSmsRequest(notification, recipientId);
                yield notificationService.sendSms(smsRequest);
            }
            case "EMAIL" -> {
                EmailRequest emailRequest = buildEmailRequest(notification, recipientId);
                yield notificationService.sendEmail(emailRequest);
            }
            case "PUSH" -> {
                PushRequest pushRequest = buildPushRequest(notification, recipientId);
                yield notificationService.sendPush(pushRequest);
            }
            case "WECHAT" -> {
//                WechatRequest wechatRequest = buildWechatRequest(notification, recipientId);
//                yield notificationService.sendWechat(wechatRequest);
                SubscribeMessageRequest request = buildWechatMiniRequest(notification, recipientId);
                yield wechatMiniAppService.sendSubscribeMessage(request);
            }
            default -> throw new IllegalArgumentException("不支持的通知渠道: " + channel);
        };
    }

    /**
     * 构建微信订阅消息请求
     */
    private SubscribeMessageRequest buildWechatMiniRequest(NotificationRecord notification, String recipientId) {
        log.debug("构建微信订阅消息请求: 通知ID={}, 接收者ID={}", notification.getId(), recipientId);
        SubscribeMessageRequest request = new SubscribeMessageRequest();
        long userId = Long.parseLong(recipientId);
        List<UserWechatBinding> userlist = userWechatBindingRepository.findByEmployeeIdAndIsDeletedFalse(userId);
        if (userlist.isEmpty()) {
            return null;
        }
        request.setOpenId(userlist.get(NumberConstants.ZERO).getOpenId());
        List<UserBindingTemplate> templateList = userBindingTemplateRepository.findByEmployeeId(userId);
        if (templateList.isEmpty()) {
            return null;
        }
        Map<String, UserBindingTemplate> map = templateList.stream().collect(Collectors.toMap(UserBindingTemplate::getTemplateId, Function.identity()));

        //根据通知类型，设置不同的模板ID
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        if (notification.getCategory() == NotificationCategory.SYSTEM) {
            //系统升级
            UserBindingTemplate userBindingTemplate = map.get(WechatMiniAppTemplateEnum.SYSTEM_UPGRADE.getTemplateId());
            if (null == userBindingTemplate) {
                return null;
            }
            request.setTemplateId(userBindingTemplate.getTemplateId());
            Instant scheduledTime = notification.getScheduledTime();
            if (null == scheduledTime) {
                scheduledTime = Instant.now();
            }
            request.setStartTime(scheduledTime.atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setEndTime(scheduledTime.plusSeconds(14400).atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setRemark(notification.getContent());
            request.setPublishTime(scheduledTime.plusSeconds(3600).atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
        } else if (notification.getCategory() == NotificationCategory.TASK) {
            //待办事项
            UserBindingTemplate userBindingTemplate = map.get(WechatMiniAppTemplateEnum.TODO_REMINDER.getTemplateId());
            if (null == userBindingTemplate) {
                return null;
            }
            request.setTemplateId(userBindingTemplate.getTemplateId());
            request.setTodoItem(notification.getTitle());
            request.setTodoContent(notification.getContent());
            request.setNoticeTime(Instant.now().atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setRemark(notification.getTitle());
        } else if (notification.getCategory() == NotificationCategory.USER) {
            //审批结果
            UserBindingTemplate userBindingTemplate = map.get(WechatMiniAppTemplateEnum.APPROVAL_RESULT.getTemplateId());
            if (null == userBindingTemplate) {
                return null;
            }
            request.setTemplateId(userBindingTemplate.getTemplateId());
            request.setNoticeTime(Instant.now().atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setNoticeContent(notification.getContent());
            request.setApprovalTime(notification.getCreatedAt().atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setApprovalResult(notification.getSubType() == NotificationSubType.APPROVAL_REJECTED ? "拒绝" : "通过");
            request.setRemark(notification.getTitle());
        } else if (notification.getCategory() == NotificationCategory.BUSINESS) {
            //企业公告
            UserBindingTemplate userBindingTemplate = map.get(WechatMiniAppTemplateEnum.BUSINESS_ANNOUNCEMENT.getTemplateId());
            if (null == userBindingTemplate) {
                return null;
            }
            request.setTemplateId(userBindingTemplate.getTemplateId());
            request.setAnnouncementTitle(notification.getTitle());
            request.setAnnouncementContent(notification.getContent());
            request.setNoticeTime(Instant.now().atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setArticleTime(notification.getCreatedAt().atZone(ZoneId.systemDefault()).format(dateTimeFormatter));
            request.setRemark(notification.getTitle());
        } else {
            return null;
        }
        log.debug("微信订阅消息请求构建完成: 模板ID={}, 接收者={}", request.getTemplateId(), request.getOpenId());
        return request;
    }

    /**
     * 构建短信请求
     */
    private SmsRequest buildSmsRequest(NotificationRecord notification, String recipientId) {
        log.debug("构建短信请求: 通知ID={}, 接收者ID={}", notification.getId(), recipientId);

        SmsRequest request = new SmsRequest();

        // 设置基本信息
        request.setRecipient(recipientId);
        request.setTenantId(notification.getTenantId());

        // 设置模板信息
        if (notification.getTemplate() != null) {
            request.setTemplateId(notification.getTemplate().getCode());
        } else {
            // 使用默认模板ID
            request.setTemplateId("2435890");
        }

        // 解析模板参数
        if (notification.getTemplateParams() != null && !notification.getTemplateParams().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> templateParams = objectMapper.readValue(notification.getTemplateParams(), Map.class);
                request.setTemplateParams(templateParams);
            } catch (JsonProcessingException e) {
                log.warn("解析短信模板参数失败: {}", e.getMessage());
                // 使用默认参数
                Map<String, Object> defaultParams = Map.of(
                    "title",
                    notification.getTitle(),
                    "content",
                    notification.getContent() != null ? notification.getContent() : ""
                );
                request.setTemplateParams(defaultParams);
            }
        } else {
            // 使用通知标题和内容作为默认参数
            Map<String, Object> defaultParams = Map.of(
                "title",
                notification.getTitle(),
                "content",
                notification.getContent() != null ? notification.getContent() : ""
            );
            request.setTemplateParams(defaultParams);
        }

        // 设置短信提供商类型（默认使用阿里云）
        request.setProviderType(SmsProviderType.ALIYUN);

        // 设置国际区号（默认中国大陆）
        request.setRegionCode("86");

        log.debug("短信请求构建完成: {}", request);
        return request;
    }

    /**
     * 构建邮件请求
     */
    private EmailRequest buildEmailRequest(NotificationRecord notification, String recipientId) {
        log.debug("构建邮件请求: 通知ID={}, 接收者ID={}", notification.getId(), recipientId);

        EmailRequest request = new EmailRequest();

        // 设置基本信息
        request.setRecipient(recipientId);
        request.setTenantId(notification.getTenantId());

        // 设置邮件主题
        request.setSubject(notification.getTitle());

        // 设置邮件内容
        String content = notification.getContent();
        if (content == null || content.trim().isEmpty()) {
            content = notification.getTitle(); // 如果内容为空，使用标题作为内容
        }

        // 如果内容不包含HTML标签，则包装为简单的HTML格式
        if (!content.contains("<") && !content.contains(">")) {
            content = String.format(
                "<html><body><h3>%s</h3><p>%s</p></body></html>",
                notification.getTitle(),
                content.replace("\n", "<br/>")
            );
        }
        request.setContent(content);

        // 设置为HTML格式
        request.setHtml(true);

        // 设置模板信息
        if (notification.getTemplate() != null) {
            request.setTemplateId(notification.getTemplate().getCode());
        }

        // 解析模板参数
        if (notification.getTemplateParams() != null && !notification.getTemplateParams().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> templateParams = objectMapper.readValue(notification.getTemplateParams(), Map.class);
                request.setTemplateParams(templateParams);
            } catch (JsonProcessingException e) {
                log.warn("解析邮件模板参数失败: {}", e.getMessage());
                // 使用默认参数
                Map<String, Object> defaultParams = Map.of(
                    "title",
                    notification.getTitle(),
                    "content",
                    notification.getContent() != null ? notification.getContent() : "",
                    "recipientId",
                    recipientId
                );
                request.setTemplateParams(defaultParams);
            }
        } else {
            // 使用通知信息作为默认参数
            Map<String, Object> defaultParams = Map.of(
                "title",
                notification.getTitle(),
                "content",
                notification.getContent() != null ? notification.getContent() : "",
                "recipientId",
                recipientId
            );
            request.setTemplateParams(defaultParams);
        }

        log.debug("邮件请求构建完成: 主题={}, 接收者={}", request.getSubject(), request.getRecipient());
        return request;
    }

    /**
     * 构建推送请求
     */
    private PushRequest buildPushRequest(NotificationRecord notification, String recipientId) {
        log.debug("构建推送请求: 通知ID={}, 接收者ID={}", notification.getId(), recipientId);

        PushRequest request = new PushRequest();

        // 设置基本信息
        request.setTenantId(notification.getTenantId());

        // 设置推送标题和内容
        request.setTitle(notification.getTitle());
        request.setContent(notification.getContent() != null ? notification.getContent() : notification.getTitle());

        // 设置推送目标类型和目标值
        request.setTargetType(PushTargetType.ALIAS);
        request.setTargets(Collections.singletonList(recipientId));

        // 设置额外数据
        Map<String, Object> extras = new HashMap<>();
        extras.put("notificationId", notification.getId());
        extras.put("category", notification.getCategory().name());
        extras.put("subType", notification.getSubType().name());
        extras.put("priority", notification.getPriority().name());

        // 添加业务相关信息
        if (notification.getBusinessId() != null) {
            extras.put("businessId", notification.getBusinessId());
        }
        if (notification.getBusinessType() != null) {
            extras.put("businessType", notification.getBusinessType());
        }

        // 解析模板参数并添加到额外数据中
        if (notification.getTemplateParams() != null && !notification.getTemplateParams().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> templateParams = objectMapper.readValue(notification.getTemplateParams(), Map.class);
                extras.putAll(templateParams);
                request.setTemplateParams(templateParams);
            } catch (JsonProcessingException e) {
                log.warn("解析推送模板参数失败: {}", e.getMessage());
            }
        }

        request.setExtras(extras);

        // 根据优先级设置推送样式
        switch (notification.getPriority()) {
            case URGENT:
                request.setNotificationStyleId(1); // 紧急样式
                request.setSilent(false);
                break;
            case HIGH:
                request.setNotificationStyleId(2); // 高优先级样式
                request.setSilent(false);
                break;
            case NORMAL:
                request.setNotificationStyleId(3); // 普通样式
                request.setSilent(false);
                break;
            case LOW:
                request.setNotificationStyleId(4); // 低优先级样式
                request.setSilent(true); // 低优先级可以静默推送
                break;
            default:
                request.setNotificationStyleId(3);
                request.setSilent(false);
        }

        // 设置模板信息
        if (notification.getTemplate() != null) {
            request.setTemplateId(notification.getTemplate().getCode());
        }

        log.debug("推送请求构建完成: 标题={}, 目标={}", request.getTitle(), request.getTargets());
        return request;
    }

    /**
     * 构建微信请求
     */
    private WechatRequest buildWechatRequest(NotificationRecord notification, String recipientId) {
        log.debug("构建微信请求: 通知ID={}, 接收者ID={}", notification.getId(), recipientId);

        WechatRequest request = new WechatRequest();

        // 设置基本信息
        request.setTenantId(notification.getTenantId());

        // 设置接收者OpenID列表
        request.setToUsers(Collections.singletonList(recipientId));

        // 判断是否使用模板消息
        if (notification.getTemplate() != null && notification.getTemplate().getCode() != null) {
            // 使用模板消息
            request.setTemplateId(notification.getTemplate().getCode());
            request.setMessageType("template");

            // 构建模板数据
            Map<String, WechatRequest.TemplateData> templateData = new HashMap<>();
            templateData.put("first", new WechatRequest.TemplateData(notification.getTitle(), "#173177"));
            templateData.put("keyword1", new WechatRequest.TemplateData(notification.getCategory().name(), "#173177"));
            templateData.put(
                "keyword2",
                new WechatRequest.TemplateData(
                    notification.getContent() != null ? notification.getContent() : notification.getTitle(),
                    "#173177"
                )
            );
            templateData.put("remark", new WechatRequest.TemplateData("请及时查看处理", "#173177"));

            // 解析模板参数
            if (notification.getTemplateParams() != null && !notification.getTemplateParams().trim().isEmpty()) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> templateParams = objectMapper.readValue(notification.getTemplateParams(), Map.class);

                    // 将模板参数转换为微信模板数据格式
                    for (Map.Entry<String, Object> entry : templateParams.entrySet()) {
                        templateData.put(entry.getKey(), new WechatRequest.TemplateData(entry.getValue().toString(), "#173177"));
                    }
                    request.setTemplateParams(templateParams);
                } catch (JsonProcessingException e) {
                    log.warn("解析微信模板参数失败: {}", e.getMessage());
                }
            }

            request.setTemplateData(templateData);

            // 设置跳转URL（如果有业务ID，可以构建详情页面URL）
            if (notification.getBusinessId() != null) {
                String url = String.format("/notification/detail/%s", notification.getId());
                request.setUrl(url);
            }
        } else {
            // 使用文本消息
            request.setMessageType("text");
            String content = String.format(
                "%s\n\n%s",
                notification.getTitle(),
                notification.getContent() != null ? notification.getContent() : ""
            );
            request.setContent(content);
        }

        // 根据优先级设置消息颜色
        String color = switch (notification.getPriority()) {
            case URGENT -> "#FF0000"; // 红色
            case HIGH -> "#FF6600"; // 橙色
            case NORMAL -> "#173177"; // 蓝色
            case LOW -> "#999999"; // 灰色
        }; // 默认颜色

        // 更新模板数据颜色
        if (request.getTemplateData() != null) {
            for (WechatRequest.TemplateData data : request.getTemplateData().values()) {
                if (data.getColor() == null || "#173177".equals(data.getColor())) {
                    data.setColor(color);
                }
            }
        }

        log.debug("微信请求构建完成: 消息类型={}, 接收者数量={}", request.getMessageType(), request.getToUsers().size());
        return request;
    }

    /**
     * 创建发送记录
     */
    private NotificationSendRecordDTO createSendRecord(
        NotificationRecord notification,
        String recipientId,
        String channel,
        NotificationResponse response
    ) {
        log.debug(
            "创建发送记录: 通知ID={}, 接收者ID={}, 渠道={}, 发送结果={}",
            notification.getId(),
            recipientId,
            channel,
            response.isSuccess()
        );

        NotificationSendRecordDTO record = new NotificationSendRecordDTO();

        // 设置基本信息
        record.setTenantId(notification.getTenantId());
        record.setRecipientId(Long.parseLong(recipientId));
        record.setRecipientType(RecipientType.USER); // 默认为用户类型

        // 设置发送渠道
        try {
            record.setChannel(com.whiskerguard.general.domain.enumeration.NotificationType.valueOf(channel.toUpperCase()));
        } catch (IllegalArgumentException e) {
            log.warn("无效的通知渠道: {}, 使用默认SMS渠道", channel);
            record.setChannel(com.whiskerguard.general.domain.enumeration.NotificationType.SMS);
        }

        // 设置发送状态
        if (response.isSuccess()) {
            record.setStatus(SendStatus.SENT);
            record.setSentTime(Instant.now());
        } else {
            record.setStatus(SendStatus.FAILED);
            record.setErrorMessage(response.getMessage());
        }

        // 设置第三方服务返回的ID
        if (response.getBusinessId() != null) {
            record.setExternalId(response.getBusinessId());
        }

        // 设置审计字段
        record.setVersion(1);
        record.setCreatedBy("system"); // 系统自动创建
        record.setCreatedAt(Instant.now());
        record.setUpdatedBy("system");
        record.setUpdatedAt(Instant.now());
        record.setIsDeleted(false);

        // 关联通知记录 - 创建一个简单的NotificationRecordDTO
        NotificationRecordDTO notificationDTO = new NotificationRecordDTO();
        notificationDTO.setId(notification.getId());
        notificationDTO.setTitle(notification.getTitle());
        notificationDTO.setCategory(notification.getCategory());
        notificationDTO.setSubType(notification.getSubType());
        record.setNotification(notificationDTO);

        log.debug("发送记录创建完成: 状态={}, 发送时间={}", record.getStatus(), record.getSentTime());
        return record;
    }
}
