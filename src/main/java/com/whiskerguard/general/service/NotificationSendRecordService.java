package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service Interface for managing {@link com.whiskerguard.general.domain.NotificationSendRecord}.
 */
public interface NotificationSendRecordService {
    /**
     * Save a notificationSendRecord.
     *
     * @param notificationSendRecordDTO the entity to save.
     * @return the persisted entity.
     */
    NotificationSendRecordDTO save(NotificationSendRecordDTO notificationSendRecordDTO);

    /**
     * Get all the notificationSendRecords.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findAll(Pageable pageable);

    /**
     * Get the "id" notificationSendRecord.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NotificationSendRecordDTO> findOne(Long id);

    /**
     * Delete the "id" notificationSendRecord.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get all the notificationSendRecords by category.
     *
     * @param category the category to filter by.
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findByCategory(NotificationCategory category, Pageable pageable);

    /**
     * Get all the notificationSendRecords by recipient type.
     *
     * @param category the category to filter by.
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findByUserIdAndCategory(NotificationCategory category, Pageable pageable);

    /**
     * 统计用户未读消息数量
     *
     * @param userId 用户ID
     * @param recipientType 接收者类型
     * @return 未读消息数量
     */
    Long countUnreadMessages(Long userId, RecipientType recipientType);

    /**
     * 统计用户未读消息数量（按分类）
     *
     * @param userId 用户ID
     * @param recipientType 接收者类型
     * @param category 通知分类
     * @return 未读消息数量
     */
    Long countUnreadMessagesByCategory(Long userId, RecipientType recipientType, NotificationCategory category);

}
