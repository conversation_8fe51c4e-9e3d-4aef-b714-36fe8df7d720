package com.whiskerguard.general.service;

import com.whiskerguard.general.model.*;

/**
 * 通知服务接口
 */
public interface NotificationService {
    /**
     * 发送短信通知
     *
     * @param request 短信请求
     * @return 通知响应
     */
    NotificationResponse sendSms(SmsRequest request);

    /**
     * 发送邮件通知
     *
     * @param request 邮件请求
     * @return 通知响应
     */
    NotificationResponse sendEmail(EmailRequest request);

    /**
     * 发送APP推送通知
     *
     * @param request 推送请求
     * @return 通知响应
     */
    NotificationResponse sendPush(PushRequest request);

    /**
     * 发送微信公众号消息
     *
     * @param request 微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendWechat(WechatRequest request);

    //
}
