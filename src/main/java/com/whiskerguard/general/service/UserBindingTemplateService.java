package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.UserBindingTemplateDTO;

import java.util.List;

/**
 * 用户绑定消息模板服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
public interface UserBindingTemplateService {

    /**
     * 保存用户绑定消息模板
     *
     * @param userBindingTemplateDTO 用户绑定消息模板DTO
     * @return 用户绑定消息模板DTO
     */
    UserBindingTemplateDTO save(UserBindingTemplateDTO userBindingTemplateDTO);

    /**
     * 获取所有用户绑定消息模板
     *
     * @return 用户绑定消息模板DTO列表
     */
    List<UserBindingTemplateDTO> findAll();

}
