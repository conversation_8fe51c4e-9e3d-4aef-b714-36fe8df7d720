package com.whiskerguard.general.service;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.service.dto.BatchSubscribeMessageRequest;
import com.whiskerguard.general.service.dto.SubscribeMessageRequest;

import java.util.Map;

/**
 * 微信小程序服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/2
 */
public interface WechatMiniAppService {

    /**
     * 发送订阅消息
     *
     * @param request 订阅消息请求
     * @return 发送结果
     */
    NotificationResponse sendSubscribeMessage(SubscribeMessageRequest request);

    /**
     * 批量发送订阅消息
     *
     * @param request 批量订阅消息请求
     * @return 发送结果
     */
    NotificationResponse sendBatchSubscribeMessage(BatchSubscribeMessageRequest request);

    /**
     * 获取小程序访问令牌
     *
     * @return 访问令牌
     */
    String getAccessToken();

    /**
     * 通过code获取用户OpenID和SessionKey
     *
     * @param code 登录凭证
     * @return 包含openid和session_key的Map
     */
    Map<String, Object> code2Session(String code);

}
