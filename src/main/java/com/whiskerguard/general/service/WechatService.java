package com.whiskerguard.general.service;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatRequest;

import java.util.Map;

/**
 * 微信公众号服务接口
 */
public interface WechatService {

    /**
     * 发送客服消息
     *
     * @param request 微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendCustomMessage(WechatRequest request);

    /**
     * 发送模板消息
     *
     * @param request 微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendTemplateMessage(WechatRequest request);

    /**
     * 获取用户信息
     *
     * @param openId 用户OpenID
     * @return 用户信息JSON字符串
     */
    String getUserInfo(String openId);

    /**
     * 获取关注者列表
     *
     * @param nextOpenId 下一个OpenID（用于分页）
     * @return 关注者列表JSON字符串
     */
    String getFollowers(String nextOpenId);

    /**
     * 获取OAuth2授权URL
     *
     * @param redirectUri 回调地址
     * @param scope       授权范围
     * @param state       状态参数
     * @return 授权URL
     */
    String getAuthorizationUrl(String redirectUri, String scope, String state);

    /**
     * 通过授权码获取用户信息
     *
     * @param code 授权码
     * @return 用户信息，包含openId、accessToken等
     */
    Map<String, Object> getUserInfoByCode(String code);
}
