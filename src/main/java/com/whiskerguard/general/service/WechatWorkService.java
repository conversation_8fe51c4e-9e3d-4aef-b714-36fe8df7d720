package com.whiskerguard.general.service;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatWorkRequest;

import java.util.List;
import java.util.Map;

/**
 * 企业微信服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
public interface WechatWorkService {

    /**
     * 发送应用消息
     *
     * @param request 企业微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendMessage(WechatWorkRequest request);

    /**
     * 发送文本消息
     *
     * @param toUser  接收用户ID（多个用|分隔）
     * @param content 消息内容
     * @return 通知响应
     */
    NotificationResponse sendTextMessage(String toUser, String content);

    /**
     * 批量发送消息
     *
     * @param requests 批量推送请求列表
     * @return 批量通知响应
     */
    List<NotificationResponse> batchSendMessage(List<WechatWorkRequest> requests);

    /**
     * 获取企业微信访问令牌
     *
     * @return 访问令牌
     */
    String getAccessToken();

    /**
     * 获取用户信息
     *
     * @param userId 企业微信用户ID
     * @return 用户信息
     */
    Map<String, Object> getUserInfo(String userId);

    /**
     * 获取部门用户列表
     *
     * @param departmentId 部门ID
     * @param fetchChild   是否递归获取子部门下的成员
     * @return 用户列表
     */
    List<Map<String, Object>> getDepartmentUsers(String departmentId, boolean fetchChild);

    /**
     * 获取部门列表
     *
     * @param departmentId 部门ID（可选，不传获取全量组织架构）
     * @return 部门列表
     */
    List<Map<String, Object>> getDepartments(String departmentId);

    /**
     * 发送群聊消息
     *
     * @param chatId  群聊ID
     * @param msgType 消息类型
     * @param content 消息内容
     * @return 通知响应
     */
    NotificationResponse sendGroupChatMessage(String chatId, String msgType, Map<String, Object> content);

    /**
     * 获取群聊列表
     *
     * @param status 群聊状态过滤（可选）：0-正常，1-跟进人离职，2-离职继承中，3-已继承
     * @param owner  群主过滤（可选）：群主的userid
     * @param cursor 分页游标，用于获取更多数据
     * @param limit  返回的最大记录数，最大1000
     * @return 群聊列表
     */
    Map<String, Object> getGroupChatList(Integer status, String owner, String cursor, Integer limit);

    /**
     * 根据手机号获取企业微信用户ID
     *
     * @param mobile 手机号
     * @return 企业微信用户ID
     */
    String getUserIdByMobile(String mobile);

    /**
     * 根据手机号获取企业微信用户信息
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    Map<String, Object> getUserInfoByMobile(String mobile);

    /**
     * 根据邮箱获取企业微信用户ID
     *
     * @param email 邮箱
     * @return 企业微信用户ID
     */
    String getUserIdByEmail(String email);
}
