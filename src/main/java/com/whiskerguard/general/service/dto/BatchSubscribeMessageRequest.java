package com.whiskerguard.general.service.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 批量订阅消息请求
 */
@Data
public class BatchSubscribeMessageRequest {

    /**
     * 用户列表
     */
    @NotEmpty
    private List<String> openIds;

    /**
     * 模板ID
     */
    @NotBlank
    private String templateId;

    /**
     * 跳转页面
     */
    private String page;

    /**
     * 消息数据
     */
    private SubscribeMessageRequest data;

}
