package com.whiskerguard.general.service.dto;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationPriority;
import com.whiskerguard.general.domain.enumeration.NotificationScope;
import com.whiskerguard.general.domain.enumeration.NotificationStatus;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;
/**
 * A DTO for the {@link com.whiskerguard.general.domain.NotificationRecord} entity.
 */
@Schema(description = "存储通知发送记录和状态信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationRecordDTO implements Serializable {
    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
    @NotNull(message = "租户ID（0 = 平台级）不能为空")
    @Schema(description = "租户ID（0 = 平台级）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;
    @NotNull(message = "通知分类不能为空")
    @Schema(description = "通知分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationCategory category;
    @NotNull(message = "通知子类型不能为空")
    @Schema(description = "通知子类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationSubType subType;
    @NotNull(message = "通知范围不能为空")
    @Schema(description = "通知范围", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationScope scope;
    @NotBlank(message = "通知标题不能为空")
    @Size(max = 200, message = "通知标题长度不符合要求")
    @Schema(description = "通知标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;
    @Size(max = 2000, message = "通知内容长度不符合要求")
    @Schema(description = "通知内容")
    private String content;
    @NotNull(message = "接收者类型不能为空")
    @Schema(description = "接收者类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private RecipientType recipientType;
    @Size(max = 2000, message = "接收者ID列表(JSON格式)长度不符合要求")
    @Schema(description = "接收者ID列表(JSON格式)")
    private String recipientIds;
    @Size(max = 500, message = "发送渠道列表(JSON格式)长度不符合要求")
    @Schema(description = "发送渠道列表(JSON格式)")
    private String channels;
    @NotNull(message = "优先级不能为空")
    @Schema(description = "优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationPriority priority;
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationStatus status;
    @Schema(description = "计划发送时间")
    private Instant scheduledTime;
    @Schema(description = "实际发送时间")
    private Instant sentTime;
    @Size(max = 100, message = "关联业务ID长度不符合要求")
    @Schema(description = "关联业务ID")
    private String businessId;
    @Size(max = 50, message = "业务类型长度不符合要求")
    @Schema(description = "业务类型")
    private String businessType;
    @Size(max = 2000, message = "模板参数(JSON格式)长度不符合要求")
    @Schema(description = "模板参数(JSON格式)")
    private String templateParams;
    @Min(value = 0, message = "重试次数不能小于最小值")
    @Max(value = 10, message = "重试次数不能大于最大值")
    @Schema(description = "重试次数")
    private Integer retryCount;
    @Size(max = 1000, message = "错误信息长度不符合要求")
    @Schema(description = "错误信息")
    private String errorMessage;
    @NotNull(message = "乐观锁版本不能为空")
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;
    @Schema(description = "创建者")
    private String createdBy;
    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;
    @Schema(description = "更新者")
    private String updatedBy;
    @NotNull(message = "更新时间不能为空")
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;
    @NotNull(message = "软删除标志不能为空")
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;
    private NotificationTemplateDTO template;
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Long getTenantId() {
        return tenantId;
    }
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    public NotificationCategory getCategory() {
        return category;
    }
    public void setCategory(NotificationCategory category) {
        this.category = category;
    }
    public NotificationSubType getSubType() {
        return subType;
    }
    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }
    public NotificationScope getScope() {
        return scope;
    }
    public void setScope(NotificationScope scope) {
        this.scope = scope;
    }
    public String getTitle() {
        return title;
    }
    public void setTitle(String title) {
        this.title = title;
    }
    public String getContent() {
        return content;
    }
    public void setContent(String content) {
        this.content = content;
    }
    public RecipientType getRecipientType() {
        return recipientType;
    }
    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }
    public String getRecipientIds() {
        return recipientIds;
    }
    public void setRecipientIds(String recipientIds) {
        this.recipientIds = recipientIds;
    }
    public String getChannels() {
        return channels;
    }
    public void setChannels(String channels) {
        this.channels = channels;
    }
    public NotificationPriority getPriority() {
        return priority;
    }
    public void setPriority(NotificationPriority priority) {
        this.priority = priority;
    }
    public NotificationStatus getStatus() {
        return status;
    }
    public void setStatus(NotificationStatus status) {
        this.status = status;
    }
    public Instant getScheduledTime() {
        return scheduledTime;
    }
    public void setScheduledTime(Instant scheduledTime) {
        this.scheduledTime = scheduledTime;
    }
    public Instant getSentTime() {
        return sentTime;
    }
    public void setSentTime(Instant sentTime) {
        this.sentTime = sentTime;
    }
    public String getBusinessId() {
        return businessId;
    }
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
    public String getBusinessType() {
        return businessType;
    }
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getTemplateParams() {
        return templateParams;
    }
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }
    public Integer getRetryCount() {
        return retryCount;
    }
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
    public String getErrorMessage() {
        return errorMessage;
    }
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }
    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public Instant getCreatedAt() {
        return createdAt;
    }
    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    public Instant getUpdatedAt() {
        return updatedAt;
    }
    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }
    public Boolean getIsDeleted() {
        return isDeleted;
    }
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
    public NotificationTemplateDTO getTemplate() {
        return template;
    }
    public void setTemplate(NotificationTemplateDTO template) {
        this.template = template;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationRecordDTO)) {
            return false;
        }
        NotificationRecordDTO notificationRecordDTO = (NotificationRecordDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, notificationRecordDTO.id);
    }
    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }
    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationRecordDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", category='" + getCategory() + "'" +
            ", subType='" + getSubType() + "'" +
            ", scope='" + getScope() + "'" +
            ", title='" + getTitle() + "'" +
            ", content='" + getContent() + "'" +
            ", recipientType='" + getRecipientType() + "'" +
            ", recipientIds='" + getRecipientIds() + "'" +
            ", channels='" + getChannels() + "'" +
            ", priority='" + getPriority() + "'" +
            ", status='" + getStatus() + "'" +
            ", scheduledTime='" + getScheduledTime() + "'" +
            ", sentTime='" + getSentTime() + "'" +
            ", businessId='" + getBusinessId() + "'" +
            ", businessType='" + getBusinessType() + "'" +
            ", templateParams='" + getTemplateParams() + "'" +
            ", retryCount=" + getRetryCount() +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", template=" + getTemplate() +
            "}";
    }
}
