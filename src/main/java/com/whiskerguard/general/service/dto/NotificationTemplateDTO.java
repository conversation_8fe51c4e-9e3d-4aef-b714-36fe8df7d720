package com.whiskerguard.general.service.dto;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;
/**
 * A DTO for the {@link com.whiskerguard.general.domain.NotificationTemplate} entity.
 */
@Schema(description = "存储通知模板配置信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationTemplateDTO implements Serializable {
    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
    @NotNull(message = "租户ID（0 = 平台级）不能为空")
    @Schema(description = "租户ID（0 = 平台级）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;
    @NotBlank(message = "模板编码不能为空")
    @Size(max = 100, message = "模板编码长度不符合要求")
    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 200, message = "模板名称长度不符合要求")
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;
    @NotNull(message = "通知分类不能为空")
    @Schema(description = "通知分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationCategory category;
    @NotNull(message = "通知子类型不能为空")
    @Schema(description = "通知子类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationSubType subType;
    @Size(max = 500, message = "标题模板长度不符合要求")
    @Schema(description = "标题模板")
    private String titleTemplate;
    @Size(max = 2000, message = "内容模板长度不符合要求")
    @Schema(description = "内容模板")
    private String contentTemplate;
    @Size(max = 500, message = "短信模板长度不符合要求")
    @Schema(description = "短信模板")
    private String smsTemplate;
    @Size(max = 2000, message = "邮件模板长度不符合要求")
    @Schema(description = "邮件模板")
    private String emailTemplate;
    @Size(max = 500, message = "推送模板长度不符合要求")
    @Schema(description = "推送模板")
    private String pushTemplate;
    @Size(max = 200, message = "支持的渠道(JSON格式)长度不符合要求")
    @Schema(description = "支持的渠道(JSON格式)")
    private String supportedChannels;
    @Size(max = 200, message = "默认渠道(JSON格式)长度不符合要求")
    @Schema(description = "默认渠道(JSON格式)")
    private String defaultChannels;
    @NotNull(message = "是否启用不能为空")
    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean enabled;
    @Size(max = 10, message = "语言长度不符合要求")
    @Schema(description = "语言")
    private String language;
    @NotNull(message = "乐观锁版本不能为空")
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;
    @Schema(description = "创建者")
    private String createdBy;
    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;
    @Schema(description = "更新者")
    private String updatedBy;
    @NotNull(message = "更新时间不能为空")
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;
    @NotNull(message = "软删除标志不能为空")
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Long getTenantId() {
        return tenantId;
    }
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public NotificationCategory getCategory() {
        return category;
    }
    public void setCategory(NotificationCategory category) {
        this.category = category;
    }
    public NotificationSubType getSubType() {
        return subType;
    }
    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }
    public String getTitleTemplate() {
        return titleTemplate;
    }
    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }
    public String getContentTemplate() {
        return contentTemplate;
    }
    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }
    public String getSmsTemplate() {
        return smsTemplate;
    }
    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }
    public String getEmailTemplate() {
        return emailTemplate;
    }
    public void setEmailTemplate(String emailTemplate) {
        this.emailTemplate = emailTemplate;
    }
    public String getPushTemplate() {
        return pushTemplate;
    }
    public void setPushTemplate(String pushTemplate) {
        this.pushTemplate = pushTemplate;
    }
    public String getSupportedChannels() {
        return supportedChannels;
    }
    public void setSupportedChannels(String supportedChannels) {
        this.supportedChannels = supportedChannels;
    }
    public String getDefaultChannels() {
        return defaultChannels;
    }
    public void setDefaultChannels(String defaultChannels) {
        this.defaultChannels = defaultChannels;
    }
    public Boolean getEnabled() {
        return enabled;
    }
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    public String getLanguage() {
        return language;
    }
    public void setLanguage(String language) {
        this.language = language;
    }
    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }
    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public Instant getCreatedAt() {
        return createdAt;
    }
    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    public Instant getUpdatedAt() {
        return updatedAt;
    }
    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }
    public Boolean getIsDeleted() {
        return isDeleted;
    }
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationTemplateDTO)) {
            return false;
        }
        NotificationTemplateDTO notificationTemplateDTO = (NotificationTemplateDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, notificationTemplateDTO.id);
    }
    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }
    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationTemplateDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", code='" + getCode() + "'" +
            ", name='" + getName() + "'" +
            ", category='" + getCategory() + "'" +
            ", subType='" + getSubType() + "'" +
            ", titleTemplate='" + getTitleTemplate() + "'" +
            ", contentTemplate='" + getContentTemplate() + "'" +
            ", smsTemplate='" + getSmsTemplate() + "'" +
            ", emailTemplate='" + getEmailTemplate() + "'" +
            ", pushTemplate='" + getPushTemplate() + "'" +
            ", supportedChannels='" + getSupportedChannels() + "'" +
            ", defaultChannels='" + getDefaultChannels() + "'" +
            ", enabled='" + getEnabled() + "'" +
            ", language='" + getLanguage() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
