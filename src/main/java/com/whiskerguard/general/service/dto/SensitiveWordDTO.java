package com.whiskerguard.general.service.dto;
import com.whiskerguard.general.domain.enumeration.LanguageType;
import com.whiskerguard.general.domain.enumeration.SensitiveCategory;
import com.whiskerguard.general.domain.enumeration.SeverityType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;
/**
 * A DTO for the {@link com.whiskerguard.general.domain.SensitiveWord} entity.
 */
@Schema(description = "存储平台级或租户级的敏感词及策略信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class SensitiveWordDTO implements Serializable {
    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
    @NotNull(message = "租户ID（0 = 平台级）不能为空")
    @Schema(description = "租户ID（0 = 平台级）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;
    @NotBlank(message = "敏感词条不能为空")
    @Schema(description = "敏感词条", requiredMode = Schema.RequiredMode.REQUIRED)
    private String term;
    @NotNull(message = "语言不能为空")
    @Schema(description = "语言", requiredMode = Schema.RequiredMode.REQUIRED)
    private LanguageType lang;
    @NotNull(message = "分类不能为空")
    @Schema(description = "分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private SensitiveCategory category;
    @NotNull(message = "严重级别不能为空")
    @Schema(description = "严重级别", requiredMode = Schema.RequiredMode.REQUIRED)
    private SeverityType severity;
    @NotNull(message = "有效开始时间不能为空")
    @Schema(description = "有效开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant validFrom;
    @NotNull(message = "有效结束时间不能为空")
    @Schema(description = "有效结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant validTo;
    @Schema(description = "备注")
    private String notes;
    @NotNull(message = "乐观锁版本不能为空")
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;
    @Schema(description = "创建者")
    private String createdBy;
    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;
    @Schema(description = "更新者")
    private String updatedBy;
    @NotNull(message = "更新时间不能为空")
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;
    @NotNull(message = "软删除标志不能为空")
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Long getTenantId() {
        return tenantId;
    }
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    public String getTerm() {
        return term;
    }
    public void setTerm(String term) {
        this.term = term;
    }
    public LanguageType getLang() {
        return lang;
    }
    public void setLang(LanguageType lang) {
        this.lang = lang;
    }
    public SensitiveCategory getCategory() {
        return category;
    }
    public void setCategory(SensitiveCategory category) {
        this.category = category;
    }
    public SeverityType getSeverity() {
        return severity;
    }
    public void setSeverity(SeverityType severity) {
        this.severity = severity;
    }
    public Instant getValidFrom() {
        return validFrom;
    }
    public void setValidFrom(Instant validFrom) {
        this.validFrom = validFrom;
    }
    public Instant getValidTo() {
        return validTo;
    }
    public void setValidTo(Instant validTo) {
        this.validTo = validTo;
    }
    public String getNotes() {
        return notes;
    }
    public void setNotes(String notes) {
        this.notes = notes;
    }
    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }
    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public Instant getCreatedAt() {
        return createdAt;
    }
    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }
    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    public Instant getUpdatedAt() {
        return updatedAt;
    }
    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }
    public Boolean getIsDeleted() {
        return isDeleted;
    }
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SensitiveWordDTO)) {
            return false;
        }
        SensitiveWordDTO sensitiveWordDTO = (SensitiveWordDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, sensitiveWordDTO.id);
    }
    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }
    // prettier-ignore
    @Override
    public String toString() {
        return "SensitiveWordDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", term='" + getTerm() + "'" +
            ", lang='" + getLang() + "'" +
            ", category='" + getCategory() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", validFrom='" + getValidFrom() + "'" +
            ", validTo='" + getValidTo() + "'" +
            ", notes='" + getNotes() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
