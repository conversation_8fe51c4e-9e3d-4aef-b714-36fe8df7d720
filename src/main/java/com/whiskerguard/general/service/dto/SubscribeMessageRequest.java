package com.whiskerguard.general.service.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 订阅消息请求
 */
@Data
public class SubscribeMessageRequest {

    /**
     * 用户OpenID
     */
    @NotBlank
    private String openId;

    /**
     * 模板ID
     */
    @NotBlank
    private String templateId;

    /**
     * 跳转页面
     */
    private String page;

    /**
     * 开始时间 2019年12月1日 00:00
     */
    private String startTime;

    /**
     * 结束时间 2019年12月1日 04:00
     */
    private String endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发布时间 2020年3月30日17:38:26
     */
    private String publishTime;

    /**
     * 公告标题
     */
    private String announcementTitle;

    /**
     * 公告内容
     */
    private String announcementContent;

    /**
     * 通知时间 2021年2月1日 12:00
     */
    private String noticeTime;

    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 发文时间 2024年9月1日 12:32:43
     */
    private String articleTime;

    /**
     * 待办事项
     */
    private String todoItem;

    /**
     * 待办内容
     */
    private String todoContent;

    /**
     * 审批时间 2019-12-24 10:10:10
     */
    private String approvalTime;

    /**
     * 审核结果 成功/失败
     */
    private String approvalResult;

}
