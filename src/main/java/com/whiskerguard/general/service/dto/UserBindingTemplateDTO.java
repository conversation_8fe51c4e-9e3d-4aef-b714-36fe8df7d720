package com.whiskerguard.general.service.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;
/**
 * 用户绑定消息模板实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
@Data
public class UserBindingTemplateDTO implements Serializable {
    private Long id;
    @NotBlank(message = "模板ID不能为空")
    @Size(max = 64, message = "模板ID长度不符合要求")
    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateId;
    @Size(max = 32, message = "模板名称长度不符合要求")
    @Schema(description = "模板名称")
    private String templateName;
    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;
    @Schema(description = "是否已绑定")
    private Boolean isBound;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UserBindingTemplateDTO userBindingTemplateDTO)) {
            return false;
        }
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, userBindingTemplateDTO.id);
    }
    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }
}
