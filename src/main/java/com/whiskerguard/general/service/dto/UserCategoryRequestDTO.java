package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户ID和分类请求DTO
 */
@Data
public class UserCategoryRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Schema(
        description = "通知分类",
        requiredMode = Schema.RequiredMode.REQUIRED,
        example = "SYSTEM"
    )
    private NotificationCategory categoryName;

}
