package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.EmailAttachment;
import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.service.EmailService;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

/**
 * 邮件服务实现
 */
@Service
public class EmailServiceImpl implements EmailService {

    private static final Logger log = LoggerFactory.getLogger(EmailServiceImpl.class);

    // 邮箱地址验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    // 邮件发送统计
    private final AtomicLong totalSent = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private final AtomicLong totalSuccess = new AtomicLong(0);

    private final ApplicationProperties applicationProperties;
    private final JavaMailSender javaMailSender;

    @Autowired
    public EmailServiceImpl(
        ApplicationProperties applicationProperties,
        @Autowired(required = false) JavaMailSender javaMailSender
    ) {
        this.applicationProperties = applicationProperties;
        this.javaMailSender = javaMailSender;

        // 启动时检查配置
        checkEmailConfiguration();
    }

    /**
     * 检查邮件配置
     */
    private void checkEmailConfiguration() {
        if (javaMailSender == null) {
            log.warn("JavaMailSender未配置！请检查以下配置：");
            log.warn("1. 确保在application.yml中配置了spring.mail相关属性");
            log.warn("2. 设置环境变量：MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD");
            log.warn("3. 或者在配置文件中直接设置邮件服务器信息");
            log.warn("示例配置：");
            log.warn("spring:");
            log.warn("  mail:");
            log.warn("    host: smtp.gmail.com");
            log.warn("    port: 587");
            log.warn("    username: <EMAIL>");
            log.warn("    password: your-app-password");
        } else {
            log.info("JavaMailSender配置成功");
        }
    }

    @Override
    public NotificationResponse send(EmailRequest request) {
        log.info("开始发送邮件: to={}, subject={}", request.getTo(), request.getSubject());

        // 验证邮件服务是否启用
        if (!applicationProperties.getNotification().getEmail().isEnabled()) {
            log.warn("邮件服务未启用");
            totalFailed.incrementAndGet();
            return NotificationResponse.failure("邮件服务未启用");
        }

        // 验证JavaMailSender是否配置
        if (javaMailSender == null) {
            log.warn("JavaMailSender未配置，无法发送邮件");
            totalFailed.incrementAndGet();
            return NotificationResponse.failure("邮件服务未配置");
        }

        // 验证邮件请求
        Map<String, String> validationErrors = validateEmailRequest(request);
        if (!validationErrors.isEmpty()) {
            log.warn("邮件请求验证失败: {}", validationErrors);
            totalFailed.incrementAndGet();
            return NotificationResponse.failure("邮件请求验证失败: " + validationErrors.toString());
        }

        try {
            totalSent.incrementAndGet();

            // 创建邮件消息
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            boolean hasAttachments = request.getAttachments() != null && !request.getAttachments().isEmpty();
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, hasAttachments, StandardCharsets.UTF_8.name());

            // 设置发件人
            String fromEmail = StringUtils.hasText(request.getFrom()) ?
                request.getFrom() : applicationProperties.getNotification().getEmail().getFrom();

            if (StringUtils.hasText(request.getFromName())) {
                message.setFrom(fromEmail, request.getFromName());
            } else {
                message.setFrom(fromEmail);
            }

            // 设置收件人
            if (request.getTo() != null && !request.getTo().isEmpty()) {
                message.setTo(request.getTo().toArray(new String[0]));
            } else if (StringUtils.hasText(request.getRecipient())) {
                // 兼容旧版本
                message.setTo(request.getRecipient());
            }

            // 设置抄送人
            if (request.getCc() != null && !request.getCc().isEmpty()) {
                message.setCc(request.getCc().toArray(new String[0]));
            }

            // 设置密送人
            if (request.getBcc() != null && !request.getBcc().isEmpty()) {
                message.setBcc(request.getBcc().toArray(new String[0]));
            }

            // 设置回复地址
            if (StringUtils.hasText(request.getReplyTo())) {
                message.setReplyTo(request.getReplyTo());
            }

            // 设置主题
            message.setSubject(request.getSubject());

            // 设置优先级
            if (request.getPriority() != null) {
                mimeMessage.setHeader("X-Priority", request.getPriority().toString());
            }

            // 设置内容
            String content = request.getContent() != null ? request.getContent() : "";
            message.setText(content, request.isHtml());


            // 添加附件
            if (hasAttachments) {
                addAttachments(message, request.getAttachments());
            }

            // 添加内嵌资源
            if (request.getInlineResources() != null && !request.getInlineResources().isEmpty()) {
                addInlineResources(message, request.getInlineResources());
            }

            // 发送邮件
            javaMailSender.send(mimeMessage);

            totalSuccess.incrementAndGet();
            log.info("邮件发送成功: to={}, subject={}", request.getTo(), request.getSubject());

            // 返回成功响应
            NotificationResponse response = NotificationResponse.success("邮件发送成功");
            response.setType(NotificationType.EMAIL);
            response.setMessageId(generateMessageId());
            return response;
        } catch (Exception e) {
            totalFailed.incrementAndGet();
            log.error("邮件发送失败: to={}, subject={}", request.getTo(), request.getSubject(), e);
            return NotificationResponse.failure("邮件发送失败: " + e.getMessage());
        }
    }

    @Override
    @Async
    public CompletableFuture<NotificationResponse> sendEmailAsync(EmailRequest emailRequest) {
        log.info("异步发送邮件: to={}, subject={}", emailRequest.getTo(), emailRequest.getSubject());
        return CompletableFuture.completedFuture(send(emailRequest));
    }

    @Override
    public List<NotificationResponse> sendBatchEmails(List<EmailRequest> emailRequests) {
        log.info("批量发送邮件: count={}", emailRequests.size());

        List<NotificationResponse> responses = new ArrayList<>();
        for (EmailRequest request : emailRequests) {
            try {
                NotificationResponse response = send(request);
                responses.add(response);

                // 添加延迟以避免发送过快
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("批量发送邮件失败: {}", request, e);
                responses.add(NotificationResponse.failure("发送失败: " + e.getMessage()));
            }
        }

        return responses;
    }

    @Override
    public NotificationResponse sendSimpleEmail(String to, String subject, String content) {
        EmailRequest request = new EmailRequest();
        request.setTo(Collections.singletonList(to));
        request.setSubject(subject);
        request.setContent(content);
        request.setHtml(false);
        return send(request);
    }

    @Override
    public NotificationResponse sendHtmlEmail(String to, String subject, String htmlContent) {
        EmailRequest request = new EmailRequest();
        request.setTo(Collections.singletonList(to));
        request.setSubject(subject);
        request.setContent(htmlContent);
        request.setHtml(true);
        return send(request);
    }

    @Override
    public NotificationResponse sendEmailWithAttachments(EmailRequest emailRequest) {
        return send(emailRequest);
    }

    @Override
    public boolean isValidEmail(String email) {
        return StringUtils.hasText(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    @Override
    public Map<String, String> validateEmailRequest(EmailRequest emailRequest) {
        Map<String, String> errors = new HashMap<>();

        // 验证收件人
        if (emailRequest.getTo() == null || emailRequest.getTo().isEmpty()) {
            if (!StringUtils.hasText(emailRequest.getRecipient())) {
                errors.put("to", "收件人不能为空");
            }
        } else {
            for (String email : emailRequest.getTo()) {
                if (!isValidEmail(email)) {
                    errors.put("to", "收件人邮箱格式不正确: " + email);
                    break;
                }
            }
        }

        // 验证主题
        if (!StringUtils.hasText(emailRequest.getSubject())) {
            errors.put("subject", "邮件主题不能为空");
        }

        // 验证内容
        if (!StringUtils.hasText(emailRequest.getContent())) {
            errors.put("content", "邮件内容不能为空");
        }

        // 验证抄送
        if (emailRequest.getCc() != null) {
            for (String email : emailRequest.getCc()) {
                if (!isValidEmail(email)) {
                    errors.put("cc", "抄送邮箱格式不正确: " + email);
                    break;
                }
            }
        }

        // 验证密送
        if (emailRequest.getBcc() != null) {
            for (String email : emailRequest.getBcc()) {
                if (!isValidEmail(email)) {
                    errors.put("bcc", "密送邮箱格式不正确: " + email);
                    break;
                }
            }
        }

        return errors;
    }

    /**
     * 添加附件
     *
     * @param message     邮件消息助手
     * @param attachments 附件列表
     */
    private void addAttachments(MimeMessageHelper message, List<EmailAttachment> attachments) {
        for (EmailAttachment attachment : attachments) {
            try {
                if (attachment.getData() != null) {
                    // 使用字节数组
                    ByteArrayResource resource = new ByteArrayResource(attachment.getData());
                    message.addAttachment(attachment.getFileName(), resource);
                } else if (StringUtils.hasText(attachment.getFilePath())) {
                    // 使用文件路径
                    File file = Path.of(attachment.getFilePath()).toFile();
                    if (file.exists()) {
                        FileSystemResource resource = new FileSystemResource(file);
                        message.addAttachment(attachment.getFileName(), resource);
                    } else {
                        log.warn("附件文件不存在: {}", attachment.getFilePath());
                    }
                } else if (attachment.getInputStream() != null) {
                    // 使用输入流
                    ByteArrayResource resource = new ByteArrayResource(
                        attachment.getInputStream().readAllBytes());
                    message.addAttachment(attachment.getFileName(), resource);
                }

                log.debug("添加附件成功: {}", attachment.getFileName());
            } catch (Exception e) {
                log.error("添加附件失败: {}", attachment.getFileName(), e);
            }
        }
    }

    /**
     * 添加内嵌资源
     *
     * @param message         邮件消息助手
     * @param inlineResources 内嵌资源映射
     */
    private void addInlineResources(MimeMessageHelper message, Map<String, String> inlineResources) {
        for (Map.Entry<String, String> entry : inlineResources.entrySet()) {
            try {
                String contentId = entry.getKey();
                String resourcePath = entry.getValue();

                File file = Path.of(resourcePath).toFile();
                if (file.exists()) {
                    FileSystemResource resource = new FileSystemResource(file);
                    message.addInline(contentId, resource);
                    log.debug("添加内嵌资源成功: contentId={}, path={}", contentId, resourcePath);
                } else {
                    log.warn("内嵌资源文件不存在: {}", resourcePath);
                }
            } catch (Exception e) {
                log.error("添加内嵌资源失败: contentId={}", entry.getKey(), e);
            }
        }
    }

    /**
     * 生成消息ID
     *
     * @return 消息ID
     */
    private String generateMessageId() {
        return "email-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
    }
}
