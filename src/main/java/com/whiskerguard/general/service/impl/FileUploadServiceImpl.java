package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.general.config.Constants;
import com.whiskerguard.general.cos.CosProperties;
import com.whiskerguard.general.cos.CosService;
import com.whiskerguard.general.service.FileUploadService;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 描述：文件上传的服务接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

    private static final Logger log = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    // 最大批量上传文件数量
    private static final int MAX_BATCH_SIZE = 50;

    // 上传进度跟踪
    private final Map<String, Map<String, Object>> uploadProgressMap = new ConcurrentHashMap<>();

    private final CosService cosService;

    @Autowired
    private CosProperties cosProperties;

    private final HttpServletRequest httpServletRequest;

    @Autowired
    public FileUploadServiceImpl(CosService cosService, HttpServletRequest httpServletRequest) {
        this.cosService = cosService;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String categoryName, String serviceName) throws Exception {
        File tempFile = null;
        Long tenantId = Long.parseLong(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_TENANT_ID));
        try {
            String originalFilename = Objects.requireNonNull(file.getOriginalFilename());
            // 创建临时文件
            tempFile = createTempFile(file);
            log.info("临时文件创建成功: {}", tempFile.getAbsolutePath());

            // 上传到腾讯云COS
            StringBuilder pathBuilder = new StringBuilder(Constants.DEFAULT_UPLOAD_DIR + serviceName).append("/");
            pathBuilder.append(tenantId).append("/");
            if (StringUtils.isNotBlank(categoryName)) {
                String[] split = categoryName.split(Constants.SPILT_MEDIAN);
                for (String part : split) {
                    pathBuilder.append(part.trim()).append("/");
                }
            }
            pathBuilder.append(LocalDate.now().format(Constants.DATE_FORMATTER)).append("/").append(originalFilename);

            String fileUrl = cosService.upload(tempFile, pathBuilder.toString());
            log.info("文件上传成功: {}", fileUrl);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "success");
            result.put("key", pathBuilder.toString());
            result.put("url", fileUrl);
            result.put("buketName", cosProperties.getBucketName());
            return result;
        } catch (IOException e) {
            log.error("文件处理失败", e);
            throw new Exception("文件上传失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    @Override
    public List<String> getFilesInDirectory(Long tenantId, String categoryName, String uploadTime, String serviceName) {
        try {
            StringBuilder pathBuilder = new StringBuilder()
                .append(Constants.DEFAULT_UPLOAD_DIR)
                .append(serviceName)
                .append("/")
                .append(tenantId)
                .append("/");
            if (StringUtils.isNotBlank(categoryName)) {
                String[] split = categoryName.split(Constants.SPILT_MEDIAN);
                for (String part : split) {
                    pathBuilder.append(part).append("/");
                }
            }
            if (StringUtils.isNotBlank(uploadTime)) {
                pathBuilder.append(uploadTime).append("/");
            }
            List<String> fileUrls = cosService.listDirectoryFiles(pathBuilder.toString());
            log.info("Successfully retrieved file list for tenantId: {}", tenantId);
            return fileUrls;
        } catch (Exception e) {
            log.error("Failed to retrieve file list for tenantId: {}, categoryName: {}, uploadTime: {},serviceName：{}",
                tenantId, categoryName, uploadTime, serviceName, e);
        }
        return List.of();
    }

    @Override
    public String getFileUrl(String key) {
        try {
            String fileUrl = cosService.generatePresignedUrl(key);
            log.info("Successfully retrieved file url for key: {}", key);
            return fileUrl;
        } catch (Exception e) {
            log.error("Failed to retrieve file url for key: {}", key, e);
        }
        return "";
    }

    private File createTempFile(MultipartFile file) throws IOException {
        // 创建临时文件
        File tempFile = File.createTempFile("upload_", "_temp");
        try (OutputStream os = Files.newOutputStream(tempFile.toPath())) {
            Files.copy(file.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        }
        return tempFile;
    }

    @Override
    public Map<String, Object> batchUploadFiles(List<MultipartFile> files, String categoryName, String serviceName) throws Exception {
        log.info("开始批量上传文件，文件数量: {}, 类别: {}, 服务: {}", files.size(), categoryName, serviceName);

        // 生成上传任务ID
        String uploadId = UUID.randomUUID().toString();

        // 验证参数
        validateBatchUploadParams(files, categoryName, serviceName);

        // 初始化上传进度
        initUploadProgress(uploadId, files.size());

        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> failureList = new ArrayList<>();

        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        try {
            for (int i = 0; i < files.size(); i++) {
                MultipartFile file = files.get(i);
                Map<String, Object> fileResult = new HashMap<>();
                fileResult.put("fileSize", file.getSize());
                fileResult.put("fileName", file.getOriginalFilename());
                try {
                    // 验证单个文件
                    validateSingleFile(file);

                    // 上传文件
                    Map<String, Object> uploadResult = uploadFile(file, categoryName, serviceName);

                    fileResult.put("success", true);
                    fileResult.put("uploadResult", uploadResult);
                    successList.add(fileResult);
                    successCount.incrementAndGet();
                    log.debug("文件上传成功: {}", file.getOriginalFilename());
                } catch (Exception e) {
                    log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                    fileResult.put("success", false);
                    fileResult.put("error", e.getMessage());
                    failureList.add(fileResult);
                    failureCount.incrementAndGet();
                }

                // 更新进度
                int processed = processedCount.incrementAndGet();
                updateUploadProgress(uploadId, processed, files.size(), successCount.get(), failureCount.get());
            }

            // 构建返回结果
            result.put("uploadId", uploadId);
            result.put("successCount", successCount.get());
            result.put("failureCount", failureCount.get());
            result.put("successFiles", successList);
            result.put("failureFiles", failureList);
            result.put("uploadTime", new Date());

            // 计算成功率
            double successRate = !files.isEmpty() ? (double) successCount.get() / files.size() * 100 : 0;
            result.put("successRate", Math.round(successRate * 100.0) / 100.0);

            log.info("批量上传完成，总数: {}, 成功: {}, 失败: {}, 成功率: {}%",
                files.size(), successCount.get(), failureCount.get(), successRate);

            return result;
        } finally {
            // 标记上传完成
            markUploadComplete(uploadId);
        }
    }

    @Override
    @Async
    public CompletableFuture<Map<String, Object>> batchUploadFilesAsync(List<MultipartFile> files, String categoryName, String serviceName) {
        try {
            Map<String, Object> result = batchUploadFiles(files, categoryName, serviceName);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步批量上传失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return CompletableFuture.completedFuture(errorResult);
        }
    }

    @Override
    public boolean isAllowedFileType(String contentType) {
        if (StringUtils.isBlank(contentType)) {
            return false;
        }
        // 检查是否在允许的文件类型列表中
        for (String allowedType : Constants.ALLOWED_FILE_TYPES) {
            if (contentType.toLowerCase().contains(allowedType.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> getUploadProgress(String uploadId) {
        return uploadProgressMap.get(uploadId);
    }

    /**
     * 验证批量上传参数
     */
    private void validateBatchUploadParams(List<MultipartFile> files, String categoryName, String serviceName) throws Exception {
        if (files == null || files.isEmpty()) {
            throw new Exception("文件列表不能为空");
        }

        if (files.size() > MAX_BATCH_SIZE) {
            throw new Exception("批量上传文件数量不能超过 " + MAX_BATCH_SIZE + " 个");
        }

        if (StringUtils.isBlank(categoryName)) {
            throw new Exception("类别名称不能为空");
        }

        if (StringUtils.isBlank(serviceName)) {
            throw new Exception("服务名称不能为空");
        }

        // 检查是否有空文件
        long emptyFileCount = files.stream().filter(MultipartFile::isEmpty).count();
        if (emptyFileCount > 0) {
            throw new Exception("存在 " + emptyFileCount + " 个空文件，请检查后重新上传");
        }

    }

    /**
     * 验证单个文件
     */
    private void validateSingleFile(MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("文件为空: " + file.getOriginalFilename());
        }

        if (!isAllowedFileType(file.getContentType())) {
            throw new Exception("不支持的文件类型: " + file.getContentType() + ", 文件: " + file.getOriginalFilename());
        }
    }

    /**
     * 初始化上传进度
     */
    private void initUploadProgress(String uploadId, int totalFiles) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("uploadId", uploadId);
        progress.put("totalFiles", totalFiles);
        progress.put("processedFiles", 0);
        progress.put("successFiles", 0);
        progress.put("failureFiles", 0);
        progress.put("progress", 0.0);
        progress.put("status", "uploading");
        progress.put("startTime", new Date());
        progress.put("lastUpdateTime", new Date());

        uploadProgressMap.put(uploadId, progress);
    }

    /**
     * 更新上传进度
     */
    private void updateUploadProgress(String uploadId, int processedFiles, int totalFiles, int successFiles, int failureFiles) {
        Map<String, Object> progress = uploadProgressMap.get(uploadId);
        if (progress != null) {
            progress.put("processedFiles", processedFiles);
            progress.put("successFiles", successFiles);
            progress.put("failureFiles", failureFiles);
            progress.put("progress", (double) processedFiles / totalFiles * 100);
            progress.put("lastUpdateTime", new Date());
        }
    }

    /**
     * 标记上传完成
     */
    private void markUploadComplete(String uploadId) {
        Map<String, Object> progress = uploadProgressMap.get(uploadId);
        if (progress != null) {
            progress.put("status", "completed");
            progress.put("endTime", new Date());
            progress.put("lastUpdateTime", new Date());
        }
    }
}
