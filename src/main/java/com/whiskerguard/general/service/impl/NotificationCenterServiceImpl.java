package com.whiskerguard.general.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.*;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.service.NotificationCenterService;
import com.whiskerguard.general.service.NotificationRecordService;
import com.whiskerguard.general.service.NotificationSendRecordService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知中心服务实现类
 *
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
@Service
@Transactional
public class NotificationCenterServiceImpl implements NotificationCenterService {

    private static final Logger log = LoggerFactory.getLogger(NotificationCenterServiceImpl.class);

    private final NotificationRecordRepository notificationRecordRepository;
    private final NotificationSendRecordRepository notificationSendRecordRepository;
    private final NotificationRecordService notificationRecordService;
    private final NotificationSendRecordService notificationSendRecordService;
    private final NotificationRecordMapper notificationRecordMapper;
    private final ObjectMapper objectMapper;
    private final HttpServletRequest request;

    public NotificationCenterServiceImpl(
        NotificationRecordRepository notificationRecordRepository,
        NotificationSendRecordRepository notificationSendRecordRepository,
        NotificationRecordService notificationRecordService,
        NotificationSendRecordService notificationSendRecordService,
        NotificationRecordMapper notificationRecordMapper,
        ObjectMapper objectMapper,
        HttpServletRequest request
    ) {
        this.notificationRecordRepository = notificationRecordRepository;
        this.notificationSendRecordRepository = notificationSendRecordRepository;
        this.notificationRecordService = notificationRecordService;
        this.notificationSendRecordService = notificationSendRecordService;
        this.notificationRecordMapper = notificationRecordMapper;
        this.objectMapper = objectMapper;
        this.request = request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NotificationRecordDTO sendNotification(NotificationRequestDTO request) {
        log.debug("发送通知请求: {}", request);

        try {
            // 创建通知记录
            NotificationRecord record = createNotificationRecord(request);

            // 保存通知记录
            record = notificationRecordRepository.save(record);

            log.info("通知创建成功: ID={}, 标题={}", record.getId(), record.getTitle());
            return notificationRecordMapper.toDto(record);
        } catch (Exception e) {
            log.error("发送通知失败: {}", request, e);
            throw new BadRequestAlertException("发送通知失败: " + e.getMessage(), "notification", "sendfailed");
        }
    }

    @Override
    public String sendBatchNotification(BatchNotificationRequestDTO request) {
        log.debug("批量发送通知请求: {}", request);

        try {
            // 生成批次ID
            String batchId = UUID.randomUUID().toString();

            // 创建批次主记录 - 用于跟踪整个批次
            NotificationRecord batchRecord = new NotificationRecord();
            batchRecord.setTenantId(getCurrentTenantId());
            batchRecord.setCategory(request.getCategory());
            batchRecord.setSubType(request.getSubType());
            batchRecord.setScope(NotificationScope.USER);
            batchRecord.setTitle(request.getTitle() + " (批次)");
            batchRecord.setContent(request.getContent());
            batchRecord.setRecipientType(RecipientType.USER);
            try {
                batchRecord.setRecipientIds(objectMapper.writeValueAsString(request.getRecipientIds()));
            } catch (JsonProcessingException e) {
                log.error("序列化接收者ID失败", e);
                batchRecord.setRecipientIds("[]");
            }
            try {
                batchRecord.setChannels(objectMapper.writeValueAsString(request.getChannels()));
            } catch (JsonProcessingException e) {
                log.error("序列化渠道失败", e);
                batchRecord.setChannels("[]");
            }
            batchRecord.setPriority(request.getPriority());
            batchRecord.setStatus(NotificationStatus.PROCESSING);
            batchRecord.setScheduledTime(request.getScheduledTime() != null ? request.getScheduledTime() : Instant.now());
            batchRecord.setBusinessId(batchId); // 使用批次ID作为业务ID
            batchRecord.setBusinessType("BATCH_NOTIFICATION");
            try {
                if (request.getTemplateParams() != null) {
                    batchRecord.setTemplateParams(objectMapper.writeValueAsString(request.getTemplateParams()));
                }
            } catch (JsonProcessingException e) {
                log.error("序列化模板参数失败", e);
            }
            batchRecord.setRetryCount(0);

            // 设置审计字段
            String username = getCurrentUser();
            batchRecord.setVersion(1);
            batchRecord.setCreatedBy(username);
            batchRecord.setCreatedAt(Instant.now());
            batchRecord.setUpdatedBy(username);
            batchRecord.setUpdatedAt(Instant.now());
            batchRecord.setIsDeleted(false);

            // 保存批次记录
            batchRecord = notificationRecordRepository.save(batchRecord);

            // 分批处理接收者
            List<Long> recipientIds = request.getRecipientIds();
            int batchSize = request.getBatchSize();
            int totalCount = recipientIds.size();

            // 创建批次状态记录 - 用于跟踪批次进度
            Map<String, Object> batchMetadata = new HashMap<>();
            batchMetadata.put("batchId", batchId);
            batchMetadata.put("totalCount", totalCount);
            batchMetadata.put("batchSize", batchSize);
            batchMetadata.put("startTime", Instant.now().toString());
            batchMetadata.put("status", "PROCESSING");
            batchMetadata.put("successCount", 0);
            batchMetadata.put("failedCount", 0);
            batchMetadata.put("sentCount", 0);

            try {
                batchRecord.setTemplateParams(objectMapper.writeValueAsString(batchMetadata));
                notificationRecordRepository.save(batchRecord); // 更新批次元数据
            } catch (JsonProcessingException e) {
                log.error("序列化批次元数据失败", e);
            }

            // 实际批次处理逻辑不变
            for (int i = 0; i < recipientIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, recipientIds.size());
                List<Long> batchRecipients = recipientIds.subList(i, endIndex);

                // 创建批次通知记录
                NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
                notificationRequest.setCategory(request.getCategory());
                notificationRequest.setSubType(request.getSubType());
                notificationRequest.setScope(NotificationScope.USER);
                notificationRequest.setTitle(request.getTitle());
                notificationRequest.setContent(request.getContent());
                notificationRequest.setRecipientType(RecipientType.USER);
                notificationRequest.setRecipientIds(batchRecipients);
                notificationRequest.setChannels(request.getChannels());
                notificationRequest.setPriority(request.getPriority());
                notificationRequest.setScheduledTime(request.getScheduledTime());
                notificationRequest.setBusinessId(request.getBusinessId());
                notificationRequest.setBusinessType(request.getBusinessType());
                notificationRequest.setTemplateId(request.getTemplateId());
                notificationRequest.setTemplateParams(request.getTemplateParams());

                // 添加批次ID作为额外元数据
                Map<String, Object> metadataParams = new HashMap<>();
                if (notificationRequest.getTemplateParams() != null) {
                    metadataParams.putAll(notificationRequest.getTemplateParams());
                }
                metadataParams.put("_batchId", batchId);
                metadataParams.put("_batchIndex", i / batchSize);
                notificationRequest.setTemplateParams(metadataParams);

                // 发送批次通知
                NotificationRecordDTO result = sendNotification(notificationRequest);

                // 更新批次处理进度
                try {
                    // 重新获取批次记录以获取最新状态
                    Optional<NotificationRecord> batchRecordOpt = notificationRecordRepository.findById(batchRecord.getId());
                    NotificationRecord updatedBatchRecord = batchRecordOpt.orElseThrow();
                    Map<String, Object> updatedMetadata = objectMapper.readValue(
                        updatedBatchRecord.getTemplateParams() != null ? updatedBatchRecord.getTemplateParams() : "{}",
                        Map.class
                    );

                    // 更新计数
                    int sentCount = (int) updatedMetadata.getOrDefault("sentCount", 0) + batchRecipients.size();
                    updatedMetadata.put("sentCount", sentCount);
                    updatedMetadata.put("progressPercentage", ((double) sentCount / totalCount) * 100);

                    // 更新状态
                    if (sentCount >= totalCount) {
                        updatedMetadata.put("status", "COMPLETED");
                        updatedMetadata.put("completedTime", Instant.now().toString());
                        updatedBatchRecord.setStatus(NotificationStatus.SENT);
                    }

                    updatedBatchRecord.setTemplateParams(objectMapper.writeValueAsString(updatedMetadata));
                    notificationRecordRepository.save(updatedBatchRecord);
                } catch (Exception e) {
                    log.error("更新批次处理进度失败", e);
                }
            }

            log.info("批量通知创建成功: 批次ID={}, 总数量={}", batchId, recipientIds.size());

            return batchId;
        } catch (Exception e) {
            log.error("批量发送通知失败: {}", request, e);
            throw new BadRequestAlertException("批量发送通知失败: " + e.getMessage(), "notification", "batchsendfailed");
        }
    }

    @Override
    public NotificationRecordDTO sendSystemNotification(SystemNotificationRequestDTO request) {
        log.debug("发送系统通知请求: {}", request);

        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.SYSTEM);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(request.getScope());
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(determineRecipientType(request.getScope()));
        notificationRequest.setRecipientIds(request.getTargetIds());
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());

        return sendNotification(notificationRequest);
    }

    @Override
    public NotificationRecordDTO sendTaskNotification(TaskNotificationRequestDTO request) {
        log.debug("发送任务通知请求: {}", request);

        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.TASK);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(NotificationScope.USER);
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(RecipientType.USER);
        notificationRequest.setRecipientIds(request.getRecipients());
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());

        return sendNotification(notificationRequest);
    }

    @Override
    public NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request) {
        log.debug("发送用户通知请求: {}", request);

        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.USER);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(NotificationScope.USER);
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(RecipientType.USER);
        notificationRequest.setRecipientIds(Collections.singletonList(request.getUserId()));
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());

        return sendNotification(notificationRequest);
    }

    @Override
    public void retryFailedNotifications() {
        log.debug("重试失败的通知");
        // TODO: 实现重试逻辑
        // 1. 查询失败的通知记录
        // 2. 重新发送
        // 3. 更新重试次数
    }

    @Override
    public void cancelScheduledNotification(Long notificationId) {
        log.debug("取消计划通知: {}", notificationId);

        Optional<NotificationRecord> recordOpt = notificationRecordRepository.findById(notificationId);
        NotificationRecord record = recordOpt.orElseThrow(() -> new BadRequestAlertException("通知记录不存在", "notification", "notfound"));

        if (record.getStatus() == NotificationStatus.SCHEDULED) {
            record.setStatus(NotificationStatus.CANCELLED);
            record.setUpdatedAt(Instant.now());
            notificationRecordRepository.save(record);

            log.info("通知已取消: ID={}", notificationId);
        } else {
            throw new BadRequestAlertException("只能取消计划中的通知", "notification", "invalid status");
        }
    }

    @Override
    public BatchNotificationStatusDTO getBatchNotificationStatus(String batchId) {
        log.debug("获取批量通知状态: {}", batchId);

        // 查找批次记录 - 使用businessId和businessType查找批次主记录
        List<NotificationRecord> batchRecords = notificationRecordRepository.findByBusinessIdAndBusinessType(batchId, "BATCH_NOTIFICATION");

        if (batchRecords.isEmpty()) {
            throw new BadRequestAlertException("找不到批次通知记录", "notification", "batch not found");
        }

        // 获取批次主记录
        NotificationRecord batchRecord = batchRecords.get(0);
        BatchNotificationStatusDTO statusDTO = new BatchNotificationStatusDTO();
        statusDTO.setBatchId(batchId);

        try {
            // 解析批次元数据
            if (batchRecord.getTemplateParams() != null) {
                Map<String, Object> metadata = objectMapper.readValue(batchRecord.getTemplateParams(), Map.class);

                // 设置基本信息
                statusDTO.setStatus((String) metadata.getOrDefault("status", mapStatusFromEnum(batchRecord.getStatus())));
                statusDTO.setTotalCount((Integer) metadata.getOrDefault("totalCount", 0));
                statusDTO.setSentCount((Integer) metadata.getOrDefault("sentCount", 0));
                statusDTO.setSuccessCount((Integer) metadata.getOrDefault("successCount", 0));
                statusDTO.setFailedCount((Integer) metadata.getOrDefault("failedCount", 0));

                // 设置时间信息
                String startTimeStr = (String) metadata.get("startTime");
                if (startTimeStr != null) {
                    statusDTO.setStartTime(Instant.parse(startTimeStr));
                } else {
                    statusDTO.setStartTime(batchRecord.getCreatedAt());
                }

                String completedTimeStr = (String) metadata.get("completedTime");
                if (completedTimeStr != null) {
                    statusDTO.setCompletedTime(Instant.parse(completedTimeStr));
                } else if (statusDTO.getStatus().equals("COMPLETED")) {
                    statusDTO.setCompletedTime(batchRecord.getUpdatedAt());
                }

                // 设置进度
                Double progress = (Double) metadata.getOrDefault("progressPercentage", 0.0);
                statusDTO.setProgressPercentage(progress);
            } else {
                // 如果没有元数据，尝试从记录状态推断
                statusDTO.setStatus(mapStatusFromEnum(batchRecord.getStatus()));
                statusDTO.setStartTime(batchRecord.getCreatedAt());
                if (batchRecord.getStatus() == NotificationStatus.SENT || batchRecord.getStatus() == NotificationStatus.FAILED) {
                    statusDTO.setCompletedTime(batchRecord.getUpdatedAt());
                }

                // 查询总任务数量 - 从相关联的通知记录中获取
                List<NotificationRecord> relatedRecords = findRelatedBatchNotifications(batchId);

                int total = relatedRecords.size();
                int sent = (int) relatedRecords.stream().filter(r -> r.getStatus() == NotificationStatus.SENT).count();
                int failed = (int) relatedRecords.stream().filter(r -> r.getStatus() == NotificationStatus.FAILED).count();

                statusDTO.setTotalCount(total);
                statusDTO.setSentCount(sent);
                statusDTO.setSuccessCount(sent - failed);
                statusDTO.setFailedCount(failed);
                statusDTO.setProgressPercentage(total > 0 ? ((double) sent / total) * 100 : 0);
            }

            // 设置错误信息
            statusDTO.setErrorMessage(batchRecord.getErrorMessage());
        } catch (Exception e) {
            log.error("解析批次状态信息失败", e);
            statusDTO.setStatus("UNKNOWN");
            statusDTO.setErrorMessage("无法解析批次状态: " + e.getMessage());
        }

        return statusDTO;
    }

    /**
     * 根据批次ID查找相关的通知记录
     */
    private List<NotificationRecord> findRelatedBatchNotifications(String batchId) {
        // 这里应该实现一个查询，找出templateParams中包含指定batchId的记录
        // 由于这需要JSON查询能力，作为简化版本，我们可以先查出所有可能的记录，然后在内存中过滤

        List<NotificationRecord> allRecords = notificationRecordRepository.findAll();
        List<NotificationRecord> relatedRecords = new ArrayList<>();

        for (NotificationRecord record : allRecords) {
            if (record.getTemplateParams() != null) {
                try {
                    Map<String, Object> params = objectMapper.readValue(record.getTemplateParams(), Map.class);
                    if (batchId.equals(params.get("_batchId"))) {
                        relatedRecords.add(record);
                    }
                } catch (Exception e) {
                    // 忽略解析错误，继续检查下一条记录
                }
            }
        }

        return relatedRecords;
    }

    /**
     * 将NotificationStatus枚举转换为字符串状态
     */
    private String mapStatusFromEnum(NotificationStatus status) {
        if (status == null) {
            return "UNKNOWN";
        }

        return switch (status) {
            case SCHEDULED -> "SCHEDULED";
            case PROCESSING -> "PROCESSING";
            case SENDING -> "SENDING";
            case SENT -> "COMPLETED";
            case READ -> "READ";
            case FAILED -> "FAILED";
            case CANCELLED -> "CANCELLED";
            default -> "UNKNOWN";
        };
    }

    @Override
    public void markAsRead(Long notificationId, Long userId) {
        log.debug("标记通知为已读: 通知ID={}, 用户ID={}", notificationId, userId);

        // 查找通知记录
        NotificationRecord notification = notificationRecordRepository
            .findById(notificationId)
            .orElseThrow(() -> new BadRequestAlertException("通知记录不存在", "notification", "notfound"));

        // 验证用户是否有权限标记该通知为已读
        boolean hasPermission = false;
        try {
            List<Long> recipientIds = objectMapper.readValue(notification.getRecipientIds(), List.class);

            // 检查用户是否在接收者列表中
            if (recipientIds.contains(userId)) {
                hasPermission = true;
            }

            // 如果是广播类通知（全部/租户/部门/角色），也允许标记为已读
            if (
                notification.getRecipientType() == RecipientType.ALL ||
                    notification.getScope() == NotificationScope.GLOBAL ||
                    notification.getScope() == NotificationScope.TENANT
            ) {
                hasPermission = true;
            }
        } catch (Exception e) {
            log.error("解析接收者列表失败", e);
        }

        if (!hasPermission) {
            throw new BadRequestAlertException("无权标记此通知为已读", "notification", "unauthorized");
        }

        // 查找或创建发送记录
        List<NotificationSendRecord> sendRecords = notificationSendRecordRepository.findByNotificationIdAndRecipientIdAndRecipientType(
            notification.getId(),
            userId,
            RecipientType.USER
        );

        NotificationSendRecord sendRecord;

        String username = getCurrentUser();
        if (sendRecords.isEmpty()) {
            // 如果找不到发送记录，创建一个新的
            sendRecord = new NotificationSendRecord();
            sendRecord.setNotification(notification);
            sendRecord.setTenantId(notification.getTenantId());
            sendRecord.setRecipientId(userId);
            sendRecord.setRecipientType(RecipientType.USER);

            // 获取通知的发送渠道
            List<NotificationType> channels = new ArrayList<>();
            try {
                channels = objectMapper.readValue(
                    notification.getChannels(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, NotificationType.class)
                );
            } catch (Exception e) {
                log.error("解析渠道信息失败", e);
            }

            // 使用第一个渠道或默认APP渠道
            NotificationType channel = channels.isEmpty() ? NotificationType.PUSH : channels.get(0);
            sendRecord.setChannel(channel);

            // 设置状态为已发送
            sendRecord.setStatus(SendStatus.SENT);
            sendRecord.setSentTime(Instant.now());

            // 设置审计字段
            sendRecord.setVersion(1);
            sendRecord.setCreatedBy(username);
            sendRecord.setCreatedAt(Instant.now());
            sendRecord.setUpdatedBy(username);
            sendRecord.setUpdatedAt(Instant.now());
            sendRecord.setIsDeleted(false);
        } else {
            // 使用现有的发送记录
            sendRecord = sendRecords.get(0);
        }

        // 标记为已读
        sendRecord.setReadTime(Instant.now());
        sendRecord.setStatus(SendStatus.READ);
        sendRecord.setUpdatedAt(Instant.now());
        sendRecord.setUpdatedBy(getCurrentUser());

        // 保存发送记录
        notificationSendRecordRepository.save(sendRecord);

        // 检查是否所有接收者都已读，如果是则更新通知记录状态
        try {
            List<Long> recipientIds = objectMapper.readValue(notification.getRecipientIds(), List.class);
            List<NotificationSendRecord> allSendRecords = notificationSendRecordRepository.findByNotificationId(notification.getId());

            // 计算已读的接收者数量
            long readCount = allSendRecords
                .stream()
                .filter(sr -> sr.getStatus() == SendStatus.READ)
                .map(NotificationSendRecord::getRecipientId)
                .distinct()
                .count();

            // 如果所有接收者都已读，更新通知状态为"已读"
            if (readCount >= recipientIds.size()) {
                notification.setStatus(NotificationStatus.READ);
                notification.setUpdatedAt(Instant.now());
                notification.setUpdatedBy(username);
                notificationRecordRepository.save(notification);
            }
        } catch (Exception e) {
            log.error("更新通知状态失败", e);
        }

        log.info("通知已标记为已读: 通知ID={}, 用户ID={}", notificationId, userId);
    }

    @Override
    public void batchMarkAsRead(List<Long> notificationIds, Long userId) {
        log.debug("批量标记通知为已读: 通知IDs={}, 用户ID={}", notificationIds, userId);

        if (notificationIds == null || notificationIds.isEmpty()) {
            throw new BadRequestAlertException("通知ID列表不能为空", "notification", "empty ids");
        }

        if (userId == null) {
            throw new BadRequestAlertException("用户ID不能为空", "notification", "user id null");
        }

        // 批量获取通知记录
        List<NotificationRecord> notifications = notificationRecordRepository.findAllById(notificationIds);

        if (notifications.isEmpty()) {
            log.warn("未找到任何指定ID的通知记录: {}", notificationIds);
            return;
        }

        // 按权限过滤通知 - 只处理用户有权限标记的通知
        List<NotificationRecord> accessibleNotifications = new ArrayList<>();

        for (NotificationRecord notification : notifications) {
            boolean hasPermission = false;
            try {
                List<Long> recipientIds = objectMapper.readValue(notification.getRecipientIds(), List.class);

                // 检查用户是否在接收者列表中
                if (recipientIds.contains(userId)) {
                    hasPermission = true;
                }

                // 如果是广播类通知（全部/租户/部门/角色），也允许标记为已读
                if (
                    notification.getRecipientType() == RecipientType.ALL ||
                        notification.getScope() == NotificationScope.GLOBAL ||
                        notification.getScope() == NotificationScope.TENANT
                ) {
                    hasPermission = true;
                }

                if (hasPermission) {
                    accessibleNotifications.add(notification);
                }
            } catch (Exception e) {
                log.error("解析通知 {} 的接收者列表失败", notification.getId(), e);
            }
        }

        if (accessibleNotifications.isEmpty()) {
            log.warn("用户 {} 没有权限标记任何指定的通知为已读", userId);
            return;
        }

        // 批量创建或更新发送记录
        List<NotificationSendRecord> sendRecordsToSave = new ArrayList<>();
        Instant now = Instant.now();

        // 查找已存在的发送记录
        List<Long> notificationIdsToProcess = accessibleNotifications.stream().map(NotificationRecord::getId).collect(Collectors.toList());

        List<NotificationSendRecord> existingSendRecords =
            notificationSendRecordRepository.findByNotificationIdInAndRecipientIdAndRecipientType(
                notificationIdsToProcess,
                userId,
                RecipientType.USER
            );

        // 构建通知ID到发送记录的映射
        Map<Long, NotificationSendRecord> existingSendRecordMap = existingSendRecords
            .stream()
            .collect(
                Collectors.toMap(
                    sr -> sr.getNotification().getId(),
                    sr -> sr,
                    (sr1, sr2) -> sr1 // 如果有重复，保留第一个
                )
            );

        // 处理每一个通知
        String username = getCurrentUser();
        for (NotificationRecord notification : accessibleNotifications) {
            NotificationSendRecord sendRecord;

            if (existingSendRecordMap.containsKey(notification.getId())) {
                // 更新已存在的发送记录
                sendRecord = existingSendRecordMap.get(notification.getId());
                sendRecord.setReadTime(now);
                sendRecord.setStatus(SendStatus.READ);
                sendRecord.setUpdatedAt(now);
                sendRecord.setUpdatedBy(username);
            } else {
                // 创建新的发送记录
                sendRecord = new NotificationSendRecord();
                sendRecord.setNotification(notification);
                sendRecord.setTenantId(notification.getTenantId());
                sendRecord.setRecipientId(userId);
                sendRecord.setRecipientType(RecipientType.USER);

                // 获取通知的发送渠道
                List<NotificationType> channels = new ArrayList<>();
                try {
                    channels = objectMapper.readValue(
                        notification.getChannels(),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, NotificationType.class)
                    );
                } catch (Exception e) {
                    log.error("解析通知 {} 的渠道信息失败", notification.getId(), e);
                }

                // 使用第一个渠道或默认APP渠道
                NotificationType channel = channels.isEmpty() ? NotificationType.PUSH : channels.get(0);
                sendRecord.setChannel(channel);

                // 设置状态为已读
                sendRecord.setStatus(SendStatus.READ);
                sendRecord.setSentTime(now);
                sendRecord.setReadTime(now);

                // 设置审计字段
                sendRecord.setVersion(1);
                sendRecord.setCreatedBy(username);
                sendRecord.setCreatedAt(now);
                sendRecord.setUpdatedBy(username);
                sendRecord.setUpdatedAt(now);
                sendRecord.setIsDeleted(false);
            }

            sendRecordsToSave.add(sendRecord);
        }

        // 批量保存发送记录
        notificationSendRecordRepository.saveAll(sendRecordsToSave);

        // 批量更新可能需要更新状态的通知记录
        List<NotificationRecord> notificationsToUpdate = new ArrayList<>();

        for (NotificationRecord notification : accessibleNotifications) {
            try {
                List<Long> recipientIds = objectMapper.readValue(notification.getRecipientIds(), List.class);
                List<NotificationSendRecord> allSendRecords = notificationSendRecordRepository.findByNotificationId(notification.getId());

                // 计算已读的接收者数量
                long readCount = allSendRecords
                    .stream()
                    .filter(sr -> sr.getStatus() == SendStatus.READ)
                    .map(NotificationSendRecord::getRecipientId)
                    .distinct()
                    .count();

                // 如果所有接收者都已读，更新通知状态为"已读"
                if (readCount >= recipientIds.size() && notification.getStatus() != NotificationStatus.READ) {
                    notification.setStatus(NotificationStatus.READ);
                    notification.setUpdatedAt(now);
                    notification.setUpdatedBy(username);
                    notificationsToUpdate.add(notification);
                }
            } catch (Exception e) {
                log.error("更新通知 {} 状态失败", notification.getId(), e);
            }
        }

        // 批量更新需要更新状态的通知记录
        if (!notificationsToUpdate.isEmpty()) {
            notificationRecordRepository.saveAll(notificationsToUpdate);
        }

        log.info("已批量标记通知为已读: 通知数量={}, 用户ID={}", accessibleNotifications.size(), userId);
    }

    @Override
    public Optional<NotificationRecordDTO> findNotificationRecord(Long id) {
        log.debug("获取通知详情: {}", id);
        // 使用NotificationRecordService的findOne方法查询通知记录
        return notificationRecordService.findOne(id);
    }

    /**
     * 创建通知记录
     */
    private NotificationRecord createNotificationRecord(NotificationRequestDTO request) throws JsonProcessingException {
        NotificationRecord record = new NotificationRecord();

        // 设置基本信息
        record.setTenantId(getCurrentTenantId());
        record.setCategory(request.getCategory());
        record.setSubType(request.getSubType());
        record.setScope(request.getScope());
        record.setTitle(request.getTitle());
        record.setContent(request.getContent());
        record.setRecipientType(request.getRecipientType());
        record.setRecipientIds(objectMapper.writeValueAsString(request.getRecipientIds()));
        record.setChannels(objectMapper.writeValueAsString(request.getChannels()));
        record.setPriority(request.getPriority());
        record.setStatus(NotificationStatus.SCHEDULED);
        record.setScheduledTime(request.getScheduledTime() != null ? request.getScheduledTime() : Instant.now());
        record.setBusinessId(request.getBusinessId());
        record.setBusinessType(request.getBusinessType());
        record.setTemplateParams(request.getTemplateParams() != null ? objectMapper.writeValueAsString(request.getTemplateParams()) : null);
        record.setRetryCount(0);

        // 设置审计字段
        String username = getCurrentUser();
        record.setVersion(1);
        record.setCreatedBy(username);
        record.setCreatedAt(Instant.now());
        record.setUpdatedBy(username);
        record.setUpdatedAt(Instant.now());
        record.setIsDeleted(false);

        return record;
    }

    /**
     * 根据通知范围确定接收者类型
     */
    private RecipientType determineRecipientType(NotificationScope scope) {
        return switch (scope) {
            case GLOBAL, TENANT -> RecipientType.ALL;
            case DEPARTMENT -> RecipientType.DEPARTMENT;
            case ROLE -> RecipientType.ROLE;
            default -> RecipientType.USER;
        };
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        return TenantContextUtil.getCurrentTenantId() == null ? 0L : TenantContextUtil.getCurrentTenantId();
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUser() {
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        return StringUtils.isBlank(username) ? "system" : username;
    }

    @Override
    @Transactional(readOnly = true)
    public Long countUnreadMessages(Long userId, RecipientType recipientType) {
        log.debug("统计用户未读消息数量: userId={}, recipientType={}", userId, recipientType);
        return notificationSendRecordService.countUnreadMessages(userId, recipientType);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<NotificationCategory, Long> countUnreadMessagesByCategory(Long userId, RecipientType recipientType) {
        log.debug("按分类统计用户未读消息数量: userId={}, recipientType={}", userId, recipientType);

        Map<NotificationCategory, Long> result = new HashMap<>();

        // 遍历所有通知分类，统计每个分类的未读消息数量
        for (NotificationCategory category : NotificationCategory.values()) {
            Long count = notificationSendRecordService.countUnreadMessagesByCategory(userId, recipientType, category);
            result.put(category, count);
        }

        return result;
    }

}
