package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.service.NotificationSendRecordService;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import com.whiskerguard.general.service.mapper.NotificationSendRecordMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.NotificationSendRecord}.
 */
@Service
@Transactional
public class NotificationSendRecordServiceImpl implements NotificationSendRecordService {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationSendRecordServiceImpl.class);

    private final NotificationSendRecordRepository notificationSendRecordRepository;

    private final NotificationSendRecordMapper notificationSendRecordMapper;

    private final HttpServletRequest httpServletRequest;

    private final NotificationRecordRepository notificationRecordRepository;

    private final NotificationRecordMapper notificationRecordMapper;

    public NotificationSendRecordServiceImpl(
        NotificationSendRecordRepository notificationSendRecordRepository,
        NotificationSendRecordMapper notificationSendRecordMapper,
        HttpServletRequest httpServletRequest,
        NotificationRecordRepository notificationRecordRepository,
        NotificationRecordMapper notificationRecordMapper
    ) {
        this.notificationSendRecordRepository = notificationSendRecordRepository;
        this.notificationSendRecordMapper = notificationSendRecordMapper;
        this.httpServletRequest = httpServletRequest;
        this.notificationRecordRepository = notificationRecordRepository;
        this.notificationRecordMapper = notificationRecordMapper;
    }

    @Override
    public NotificationSendRecordDTO save(NotificationSendRecordDTO notificationSendRecordDTO) {
        LOG.debug("Request to save NotificationSendRecord : {}", notificationSendRecordDTO);
        NotificationSendRecord notificationSendRecord = notificationSendRecordMapper.toEntity(notificationSendRecordDTO);
        notificationSendRecord = notificationSendRecordRepository.save(notificationSendRecord);
        return notificationSendRecordMapper.toDto(notificationSendRecord);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NotificationSendRecords");
        return notificationSendRecordRepository.findAll(pageable).map(notificationSendRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NotificationSendRecordDTO> findOne(Long id) {
        LOG.debug("Request to get NotificationSendRecord : {}", id);
        return notificationSendRecordRepository.findById(id).map(notificationSendRecordMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NotificationSendRecord : {}", id);
        notificationSendRecordRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findByCategory(NotificationCategory category, Pageable pageable) {
        LOG.debug("Request to get NotificationSendRecords by category: {}", category);
        return notificationSendRecordRepository.findByCategory(category, pageable).map(notificationSendRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findByUserIdAndCategory(NotificationCategory category, Pageable pageable) {
        Long tenantId = Long.parseLong(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_TENANT_ID));
        Long userId = Long.parseLong(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_ID));
        LOG.debug("Request to get NotificationSendRecords by userId: {} and category: {}", userId, category);
        if (category == NotificationCategory.USER || category == NotificationCategory.TASK) {
            List<NotificationRecord> list = notificationRecordRepository.findByTenantIdAndCategory(tenantId, category);
            if (list.isEmpty()) {
                return Page.empty(pageable);
            }
            List<Long> notificationIds = list.stream().map(NotificationRecord::getId).toList();
            Page<NotificationSendRecord> page = notificationSendRecordRepository.findByRecipientIdAndNotificationIdIn(userId, notificationIds, pageable);
            List<NotificationSendRecord> content = page.getContent();
            if (content.isEmpty()) {
                return Page.empty(pageable);
            }
            List<NotificationSendRecordDTO> result = new ArrayList<>();
            for (NotificationSendRecord notificationSendRecord : content) {
                NotificationSendRecordDTO dto = notificationSendRecordMapper.toDto(notificationSendRecord);
                dto.setNotification(notificationRecordMapper.toDto(notificationSendRecord.getNotification()));
                result.add(dto);
            }
            return new PageImpl<>(result, pageable, page.getTotalElements());
        } else if (category == NotificationCategory.BUSINESS) {
            Page<NotificationRecord> page = notificationRecordRepository.pageByTenantIdAndCategory(tenantId, category, pageable);
            return listNotificationSendRecord(pageable, page);
        } else if (category == NotificationCategory.SYSTEM) {
            Page<NotificationRecord> page = notificationRecordRepository.pageByCategory(category, pageable);
            return listNotificationSendRecord(pageable, page);
        }
        LOG.warn("Unsupported notification category: {}", category);
        return Page.empty(pageable);
    }

    /**
     * 根据分页信息和通知记录分页结果，重新组合成Page<NotificationSendRecordDTO>
     *
     * @param pageable 分页信息
     * @param page     通知记录分页结果
     * @return Page<NotificationSendRecordDTO>
     */
    private Page<NotificationSendRecordDTO> listNotificationSendRecord(Pageable pageable, Page<NotificationRecord> page) {
        List<NotificationRecord> list = page.getContent();
        if (list.isEmpty()) {
            return Page.empty(pageable);
        }
        List<NotificationSendRecordDTO> result = new ArrayList<>();
        for (NotificationRecord notificationRecord : list) {
            NotificationSendRecordDTO dto = new NotificationSendRecordDTO();
            dto.setSentTime(notificationRecord.getSentTime());
            dto.setNotification(notificationRecordMapper.toDto(notificationRecord));
            result.add(dto);
        }
        return new PageImpl<>(result, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public Long countUnreadMessages(Long userId, RecipientType recipientType) {
        LOG.debug("Request to count unread messages for user: {}, recipientType: {}", userId, recipientType);
        return notificationSendRecordRepository.countUnreadByRecipientIdAndRecipientType(userId, recipientType);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countUnreadMessagesByCategory(Long userId, RecipientType recipientType, NotificationCategory category) {
        LOG.debug("Request to count unread messages for user: {}, recipientType: {}, category: {}",
            userId, recipientType, category);
        return notificationSendRecordRepository.countUnreadByRecipientIdAndRecipientTypeAndCategory(
            userId, recipientType, category);
    }
}
