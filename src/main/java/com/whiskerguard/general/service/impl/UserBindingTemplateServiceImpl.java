package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.general.domain.UserBindingTemplate;
import com.whiskerguard.general.enums.WechatMiniAppTemplateEnum;
import com.whiskerguard.general.repository.UserBindingTemplateRepository;
import com.whiskerguard.general.service.UserBindingTemplateService;
import com.whiskerguard.general.service.dto.UserBindingTemplateDTO;
import com.whiskerguard.general.service.mapper.UserBindingTemplateMapper;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户绑定消息模板服务接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
@Service
@Transactional
public class UserBindingTemplateServiceImpl implements UserBindingTemplateService {

    private static final Logger LOG = LoggerFactory.getLogger(UserBindingTemplateServiceImpl.class);

    private final UserBindingTemplateRepository userBindingTemplateRepository;

    private final UserBindingTemplateMapper userBindingTemplateMapper;

    private final HttpServletRequest httpServletRequest;

    public UserBindingTemplateServiceImpl(
        UserBindingTemplateRepository userBindingTemplateRepository,
        UserBindingTemplateMapper userBindingTemplateMapper,
        HttpServletRequest httpServletRequest
    ) {
        this.userBindingTemplateRepository = userBindingTemplateRepository;
        this.userBindingTemplateMapper = userBindingTemplateMapper;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public UserBindingTemplateDTO save(UserBindingTemplateDTO userBindingTemplateDTO) {
        LOG.debug("Request to save UserBindingTemplate : {}", userBindingTemplateDTO);
        Long tenantId = TenantContextUtil.getCurrentTenantId();
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_ID));
        String username = HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_NAME);
        if (userBindingTemplateDTO.getIsBound()) {
            UserBindingTemplate userBindingTemplate = userBindingTemplateMapper.toEntity(userBindingTemplateDTO);
            userBindingTemplate.setTenantId(tenantId);
            userBindingTemplate.setEmployeeId(userId);
            userBindingTemplate.setTemplateName(WechatMiniAppTemplateEnum.findByTemplateId(userBindingTemplateDTO.getTemplateId()).getTitle());
            userBindingTemplate.setVersion(NumberConstants.ONE);
            userBindingTemplate.setCreatedAt(Instant.now());
            userBindingTemplate.setCreatedBy(username);
            userBindingTemplate.setUpdatedAt(Instant.now());
            userBindingTemplate.setUpdatedBy(username);
            userBindingTemplate.setIsDeleted(Boolean.FALSE);
            userBindingTemplate = userBindingTemplateRepository.save(userBindingTemplate);
            return userBindingTemplateMapper.toDto(userBindingTemplate);
        } else {
            UserBindingTemplate userBindingTemplate = userBindingTemplateRepository.
                findByEmployeeIdAndTemplateId(userId, userBindingTemplateDTO.getTemplateId());
            if (null == userBindingTemplate) {
                throw new BadRequestAlertException("未找到绑定记录", "UserBindingTemplate", "not found");
            }
            userBindingTemplate.setIsDeleted(Boolean.TRUE);
            userBindingTemplate = userBindingTemplateRepository.save(userBindingTemplate);
            return userBindingTemplateMapper.toDto(userBindingTemplate);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserBindingTemplateDTO> findAll() {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_ID));
        LOG.debug("Request to get all UserBindingTemplates for user: {}", userId);
        List<UserBindingTemplateDTO> result = new ArrayList<>();
        List<UserBindingTemplate> list = userBindingTemplateRepository.findByEmployeeId(userId);
        Map<String, UserBindingTemplate> map = list.stream().collect(Collectors.toMap(UserBindingTemplate::getTemplateId, Function.identity()));

        UserBindingTemplateDTO userBindingTemplateDto;
        for (WechatMiniAppTemplateEnum templateEnum : WechatMiniAppTemplateEnum.values()) {
            userBindingTemplateDto = new UserBindingTemplateDTO();
            userBindingTemplateDto.setTemplateId(templateEnum.getTemplateId());
            userBindingTemplateDto.setTemplateName(templateEnum.getTitle());
            UserBindingTemplate userBindingTemplate = map.get(templateEnum.getTemplateId());
            if (null != userBindingTemplate) {
                userBindingTemplateDto.setIsBound(Boolean.TRUE);
                userBindingTemplate.setCreatedAt(userBindingTemplate.getCreatedAt());
            } else {
                userBindingTemplateDto.setIsBound(Boolean.FALSE);
            }
            result.add(userBindingTemplateDto);
        }
        return result;
    }

}
