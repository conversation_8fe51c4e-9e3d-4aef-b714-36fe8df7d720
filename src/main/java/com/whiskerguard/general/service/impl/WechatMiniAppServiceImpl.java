package com.whiskerguard.general.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.enums.WechatMiniAppTemplateEnum;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.service.WechatMiniAppService;
import com.whiskerguard.general.service.dto.BatchSubscribeMessageRequest;
import com.whiskerguard.general.service.dto.SubscribeMessageRequest;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 微信小程序服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/2
 */
@Service
public class WechatMiniAppServiceImpl implements WechatMiniAppService {

    private static final Logger log = LoggerFactory.getLogger(WechatMiniAppServiceImpl.class);

    private final ApplicationProperties applicationProperties;

    private WxMaService wxMaService;

    @Autowired
    public WechatMiniAppServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @PostConstruct
    public void init() {
        try {
            ApplicationProperties.Notification.Wechat.MiniApp miniAppProperties =
                applicationProperties.getNotification().getWechat().getMiniApp();

            if (!miniAppProperties.isEnabled()) {
                log.warn("微信小程序服务未启用");
                return;
            }

            WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
            config.setAppid(miniAppProperties.getAppId());
            config.setSecret(miniAppProperties.getAppSecret());
            config.setToken(miniAppProperties.getToken());
            config.setAesKey(miniAppProperties.getAesKey());

            wxMaService = new WxMaServiceImpl();
            wxMaService.setWxMaConfig(config);

            log.info("微信小程序服务初始化成功，AppId: {}", miniAppProperties.getAppId());
        } catch (Exception e) {
            log.error("微信小程序服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("微信小程序服务已关闭");
    }

    @Override
    public NotificationResponse sendSubscribeMessage(SubscribeMessageRequest request) {
        if (wxMaService == null) {
            return NotificationResponse.failure("微信小程序服务未初始化");
        }
        if (request == null) {
            return NotificationResponse.failure("请求参数不能为空");
        }

        String openId = request.getOpenId();
        String templateId = request.getTemplateId();
        String page = request.getPage();
        try {
            WxMaSubscribeMessage message = new WxMaSubscribeMessage();
            message.setToUser(openId);
            message.setTemplateId(templateId);
            message.setPage(page);

            // 转换数据格式
            List<WxMaSubscribeMessage.MsgData> dataList = buildSubscribeMessageData(request, templateId);
            message.setData(dataList);
            wxMaService.getMsgService().sendSubscribeMsg(message);

            log.info("小程序订阅消息发送成功，OpenId: {}, TemplateId: {}", openId, templateId);
            return NotificationResponse.success("订阅消息发送成功", openId);
        } catch (WxErrorException e) {
            log.error("小程序订阅消息发送失败，OpenId: {}, 错误: {}", openId, e.getMessage());
            return NotificationResponse.failure("订阅消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public NotificationResponse sendBatchSubscribeMessage(BatchSubscribeMessageRequest request) {
        List<String> openIds = request.getOpenIds();
        if (openIds == null || openIds.isEmpty()) {
            return NotificationResponse.failure("用户列表不能为空");
        }

        int successCount = 0;
        int failureCount = 0;
        List<String> failedOpenIds = new ArrayList<>();

        for (String openId : openIds) {
            NotificationResponse response = sendSubscribeMessage(request.getData());
            if (response.isSuccess()) {
                successCount++;
            } else {
                failureCount++;
                failedOpenIds.add(openId);
            }
        }

        String message = String.format("批量发送完成，成功: %d, 失败: %d", successCount, failureCount);

        if (failureCount == 0) {
            return NotificationResponse.success(message);
        } else {
            message += " (失败用户: " + String.join(",", failedOpenIds) + ")";
            return NotificationResponse.failure(message);
        }
    }

    @Override
    public String getAccessToken() {
        if (wxMaService == null) {
            return null;
        }

        try {
            return wxMaService.getAccessToken();
        } catch (WxErrorException e) {
            log.error("获取小程序AccessToken失败", e);
            return null;
        }
    }

    @Override
    public Map<String, Object> code2Session(String code) {
        if (wxMaService == null) {
            return Collections.singletonMap("error", "微信小程序服务未初始化");
        }

        try {
            WxMaJscode2SessionResult result = wxMaService.getUserService().getSessionInfo(code);
            Map<String, Object> response = new HashMap<>();
            response.put("openid", result.getOpenid());
            response.put("sessionKey", result.getSessionKey());
            response.put("unionid", result.getUnionid());

            return response;
        } catch (WxErrorException e) {
            log.error("code2Session失败，code: {}", code, e);
            return Collections.singletonMap("error", e.getMessage());
        }
    }

    //根据模板组装消息
    public List<WxMaSubscribeMessage.MsgData> buildSubscribeMessageData(SubscribeMessageRequest request, String templateId) {
        WechatMiniAppTemplateEnum templateEnum = WechatMiniAppTemplateEnum.findByTemplateId(templateId);
        if (templateEnum == null) {
            log.error("未找到对应的模板: {}", templateId);
            return Collections.emptyList();
        }
        List<WxMaSubscribeMessage.MsgData> dataList = new ArrayList<>();
        if (templateEnum == WechatMiniAppTemplateEnum.SYSTEM_UPGRADE) {
            dataList.add(new WxMaSubscribeMessage.MsgData("date1", request.getStartTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("date2", request.getEndTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing3", request.getRemark()));
            dataList.add(new WxMaSubscribeMessage.MsgData("time4", request.getPublishTime()));
        } else if (templateEnum == WechatMiniAppTemplateEnum.BUSINESS_ANNOUNCEMENT) {
            dataList.add(new WxMaSubscribeMessage.MsgData("thing2", request.getAnnouncementTitle()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing4", request.getAnnouncementContent()));
            dataList.add(new WxMaSubscribeMessage.MsgData("time6", request.getNoticeTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("time8", request.getArticleTime()));
        } else if (templateEnum == WechatMiniAppTemplateEnum.TODO_REMINDER) {
            dataList.add(new WxMaSubscribeMessage.MsgData("thing3", request.getTodoItem()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing4", request.getTodoContent()));
            dataList.add(new WxMaSubscribeMessage.MsgData("time5", request.getNoticeTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing6", request.getRemark()));
        } else if (templateEnum == WechatMiniAppTemplateEnum.APPROVAL_RESULT) {
            dataList.add(new WxMaSubscribeMessage.MsgData("time9", request.getNoticeTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing10", request.getNoticeContent()));
            dataList.add(new WxMaSubscribeMessage.MsgData("date2", request.getApprovalTime()));
            dataList.add(new WxMaSubscribeMessage.MsgData("thing4", request.getRemark()));
            dataList.add(new WxMaSubscribeMessage.MsgData("phrase5", request.getApprovalResult()));
        }
        return dataList;
    }

}
