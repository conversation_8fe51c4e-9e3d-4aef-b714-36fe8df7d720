package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatWorkRequest;
import com.whiskerguard.general.service.WechatWorkService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业微信服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
@Service
public class WechatWorkServiceImpl implements WechatWorkService {

    private static final Logger log = LoggerFactory.getLogger(WechatWorkServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private WxCpService wxCpService;

    public WechatWorkServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @PostConstruct
    public void init() {
        try {
            ApplicationProperties.Notification.WechatWork wechatWorkProperties =
                applicationProperties.getNotification().getWechatWork();

            if (!wechatWorkProperties.isEnabled()) {
                log.warn("企业微信服务未启用");
                return;
            }

            WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
            config.setCorpId(wechatWorkProperties.getCorpId());
            config.setCorpSecret(wechatWorkProperties.getCorpSecret());
            config.setAgentId(Integer.valueOf(wechatWorkProperties.getAgentId()));

            if (wechatWorkProperties.getToken() != null && !wechatWorkProperties.getToken().isEmpty()) {
                config.setToken(wechatWorkProperties.getToken());
            }

            if (wechatWorkProperties.getAesKey() != null && !wechatWorkProperties.getAesKey().isEmpty()) {
                config.setAesKey(wechatWorkProperties.getAesKey());
            }

            wxCpService = new WxCpServiceImpl();
            wxCpService.setWxCpConfigStorage(config);

            log.info("企业微信服务初始化成功，CorpId: {}, AgentId: {}",
                wechatWorkProperties.getCorpId(), wechatWorkProperties.getAgentId());
        } catch (Exception e) {
            log.error("企业微信服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("企业微信服务已关闭");
    }

    @Override
    public NotificationResponse sendMessage(WechatWorkRequest request) {
        if (wxCpService == null) {
            return NotificationResponse.failure("企业微信服务未初始化");
        }

        try {
            WxCpMessage message = buildWxCpMessage(request);
            WxCpMessageSendResult result = wxCpService.getMessageService().send(message);

            if (result.getErrCode() == 0) {
                return NotificationResponse.success("消息发送成功")
                    .messageId(result.getMsgId())
                    .timestamp(Instant.now());
            } else {
                return NotificationResponse.failure("消息发送失败: " + result.getErrMsg())
                    .errorCode(String.valueOf(result.getErrCode()));
            }
        } catch (WxErrorException e) {
            log.error("企业微信消息发送失败", e);
            return NotificationResponse.failure("消息发送失败: " + e.getMessage())
                .errorCode(String.valueOf(e.getError().getErrorCode()));
        } catch (Exception e) {
            log.error("企业微信消息发送异常", e);
            return NotificationResponse.failure("消息发送异常: " + e.getMessage());
        }
    }

    @Override
    public NotificationResponse sendTextMessage(String toUser, String content) {
        WechatWorkRequest request = new WechatWorkRequest();
        request.setMsgType("text");
        request.setToUser(toUser);
        request.setAgentId(getDefaultAgentId());

        WechatWorkRequest.TextMessage textMessage = new WechatWorkRequest.TextMessage();
        textMessage.setContent(content);
        request.setText(textMessage);

        return sendMessage(request);
    }

    @Override
    public List<NotificationResponse> batchSendMessage(List<WechatWorkRequest> requests) {
        return requests.stream()
            .map(this::sendMessage)
            .collect(Collectors.toList());
    }

    @Override
    public String getAccessToken() {
        if (wxCpService == null) {
            return null;
        }

        try {
            return wxCpService.getAccessToken();
        } catch (WxErrorException e) {
            log.error("获取企业微信访问令牌失败", e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getUserInfo(String userId) {
        if (wxCpService == null) {
            return Collections.singletonMap("error", "企业微信服务未初始化");
        }

        try {
            WxCpUser user = wxCpService.getUserService().getById(userId);
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userid", user.getUserId());
            userInfo.put("name", user.getName());
            userInfo.put("mobile", user.getMobile());
            userInfo.put("email", user.getEmail());
            userInfo.put("department", user.getDepartIds());
            userInfo.put("position", user.getPosition());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("status", user.getStatus());
            userInfo.put("enable", user.getEnable());
            return userInfo;
        } catch (WxErrorException e) {
            log.error("获取企业微信用户信息失败，UserId: {}", userId, e);
            return Collections.singletonMap("error", e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDepartmentUsers(String departmentId, boolean fetchChild) {
        if (wxCpService == null) {
            return Collections.emptyList();
        }

        try {
            List<WxCpUser> users = wxCpService.getUserService().listByDepartment(
                Long.valueOf(departmentId), fetchChild, 0);

            return users.stream().map(user -> {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userid", user.getUserId());
                userInfo.put("name", user.getName());
                userInfo.put("mobile", user.getMobile());
                userInfo.put("email", user.getEmail());
                userInfo.put("department", user.getDepartIds());
                userInfo.put("position", user.getPosition());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("status", user.getStatus());
                return userInfo;
            }).collect(Collectors.toList());
        } catch (WxErrorException e) {
            log.error("获取部门用户列表失败，DepartmentId: {}", departmentId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Map<String, Object>> getDepartments(String departmentId) {
        if (wxCpService == null) {
            return Collections.emptyList();
        }

        try {
            Long deptId = departmentId != null ? Long.valueOf(departmentId) : null;
            return wxCpService.getDepartmentService().list(deptId).stream().map(dept -> {
                Map<String, Object> deptInfo = new HashMap<>();
                deptInfo.put("id", dept.getId());
                deptInfo.put("name", dept.getName());
                deptInfo.put("parentid", dept.getParentId());
                deptInfo.put("order", dept.getOrder());
                return deptInfo;
            }).collect(Collectors.toList());
        } catch (WxErrorException e) {
            log.error("获取部门列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建企业微信消息对象
     */
    private WxCpMessage buildWxCpMessage(WechatWorkRequest request) {

        return switch (request.getMsgType()) {
            case "text" -> WxCpMessage.TEXT()
                .agentId(request.getAgentId())
                .toUser(request.getToUser())
                .toParty(request.getToParty())
                .toTag(request.getToTag())
                .content(request.getText().getContent())
                .build();
            case "textcard" -> {
                WechatWorkRequest.TextCardMessage textCard = request.getTextCard();
                yield WxCpMessage.TEXTCARD()
                    .agentId(request.getAgentId())
                    .toUser(request.getToUser())
                    .toParty(request.getToParty())
                    .toTag(request.getToTag())
                    .title(textCard.getTitle())
                    .description(textCard.getDescription())
                    .url(textCard.getUrl())
                    .btnTxt(textCard.getBtnTxt())
                    .build();
            }
            case "markdown" -> WxCpMessage.MARKDOWN()
                .agentId(request.getAgentId())
                .toUser(request.getToUser())
                .toParty(request.getToParty())
                .toTag(request.getToTag())
                .content(request.getMarkdown().getContent())
                .build();
            case "image" -> WxCpMessage.IMAGE()
                .agentId(request.getAgentId())
                .toUser(request.getToUser())
                .toParty(request.getToParty())
                .toTag(request.getToTag())
                .mediaId(request.getImage().getMediaId())
                .build();
            case "voice" -> WxCpMessage.VOICE()
                .agentId(request.getAgentId())
                .toUser(request.getToUser())
                .toParty(request.getToParty())
                .toTag(request.getToTag())
                .mediaId(request.getVoice().getMediaId())
                .build();
            case "video" -> {
                WechatWorkRequest.VideoMessage video = request.getVideo();
                yield WxCpMessage.VIDEO()
                    .agentId(request.getAgentId())
                    .toUser(request.getToUser())
                    .toParty(request.getToParty())
                    .toTag(request.getToTag())
                    .mediaId(video.getMediaId())
                    .title(video.getTitle())
                    .description(video.getDescription())
                    .build();
            }
            case "file" -> WxCpMessage.FILE()
                .agentId(request.getAgentId())
                .toUser(request.getToUser())
                .toParty(request.getToParty())
                .toTag(request.getToTag())
                .mediaId(request.getFile().getMediaId())
                .build();
            default -> throw new IllegalArgumentException("不支持的消息类型: " + request.getMsgType());
        };
    }

    @Override
    public NotificationResponse sendGroupChatMessage(String chatId, String msgType, Map<String, Object> content) {
        // 企业微信群聊消息发送功能
        log.warn("企业微信群聊消息发送功能暂未实现");
        return NotificationResponse.failure("群聊消息发送功能暂未实现");
    }


    @Override
    public Map<String, Object> getGroupChatList(Integer status, String owner, String cursor, Integer limit) {
        try {
            // 使用企业微信客户联系人API获取群聊列表
            Map<String, Object> result = new HashMap<>();

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            if (status != null) {
                params.put("status_filter", status);
            }
            if (owner != null && !owner.isEmpty()) {
                params.put("owner_filter", Collections.singletonList(owner));
            }
            if (cursor != null && !cursor.isEmpty()) {
                params.put("cursor", cursor);
            }
            if (limit != null && limit > 0) {
                params.put("limit", Math.min(limit, 1000)); // 最大1000
            } else {
                params.put("limit", 100); // 默认100
            }

            log.info("获取企业微信群聊列表，参数: {}", params);

            if (wxCpService == null) {
                log.warn("企业微信服务未初始化，返回模拟数据");
            }

            // 暂时返回模拟数据，等待正确的API实现
            result.put("errcode", 0);
            result.put("errmsg", "ok");
            result.put("group_chat_list", Collections.emptyList());
            result.put("next_cursor", "");

            log.warn("企业微信群聊列表获取功能暂未实现，返回空列表");
            return result;

        } catch (Exception e) {
            log.error("获取企业微信群聊列表失败", e);
            return Collections.singletonMap("error", e.getMessage());
        }
    }


    @Override
    public String getUserIdByMobile(String mobile) {
        if (wxCpService == null) {
            return null;
        }

        try {
            // 暂时返回null，等待正确的API实现
            log.warn("企业微信通过手机号获取用户ID功能暂未实现，Mobile: {}", mobile);
            return null;
        } catch (Exception e) {
            log.error("根据手机号获取企业微信用户ID失败，Mobile: {}", mobile, e);
            return null;
        }
    }

    @Override
    public String getUserIdByEmail(String email) {
        if (wxCpService == null) {
            return null;
        }

        try {
            // 暂时返回null，等待正确的API实现
            log.warn("企业微信通过邮箱获取用户ID功能暂未实现，Email: {}", email);
            return null;
        } catch (Exception e) {
            log.error("根据邮箱获取企业微信用户ID失败，Email: {}", email, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getUserInfoByMobile(String mobile) {
        if (wxCpService == null) {
            return Collections.singletonMap("error", "企业微信服务未初始化");
        }

        try {
            // 暂时返回错误信息，等待正确的API实现
            log.warn("企业微信通过手机号获取用户信息功能暂未实现，Mobile: {}", mobile);
            return Collections.singletonMap("error", "通过手机号获取用户信息功能暂未实现");
        } catch (Exception e) {
            log.error("根据手机号获取企业微信用户信息失败，Mobile: {}", mobile, e);
            return Collections.singletonMap("error", e.getMessage());
        }
    }
    
    /**
     * 获取默认应用ID
     */
    private Integer getDefaultAgentId() {
        ApplicationProperties.Notification.WechatWork wechatWorkProperties =
            applicationProperties.getNotification().getWechatWork();
        return Integer.valueOf(wechatWorkProperties.getAgentId());
    }
}
