package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.UserBindingTemplate;
import com.whiskerguard.general.service.dto.UserBindingTemplateDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link UserBindingTemplate} and its DTO {@link UserBindingTemplateDTO}.
 */
@Mapper(componentModel = "spring")
public interface UserBindingTemplateMapper extends EntityMapper<UserBindingTemplateDTO, UserBindingTemplate> {}
