package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.config.Constants;
import com.whiskerguard.general.cos.CosService;
import com.whiskerguard.general.service.FileUploadService;
import com.whiskerguard.general.service.dto.FileOperationRequestDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 文件上传相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
@RestController
@RequestMapping("/api/file")
@Tag(name = "文件上传", description = "文件上传相关API，支持单文件和批量上传")
public class FileUploadResource {

    private static final Logger LOG = LoggerFactory.getLogger(FileUploadResource.class);

    private final FileUploadService fileUploadService;
    private final CosService cosService;

    public FileUploadResource(FileUploadService fileUploadService, CosService cosService) {
        this.fileUploadService = fileUploadService;
        this.cosService = cosService;
    }

    /**
     * 处理文件上传请求的方法。
     *
     * @param file         要上传的文件，通过请求参数 "file" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @return 如果文件上传成功，返回包含文件URL的200 OK响应；
     * 如果文件为空，返回400 Bad Request响应；
     * 如果文件类型不被允许，返回400 Bad Request响应；
     * 如果上传过程中发生异常，返回500 Internal Server Error响应。
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
        @RequestParam(name = "file") MultipartFile file, @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName", required = false) String categoryName
    ) {
        LOG.info("Receiving file upload request for file: {}", file.getOriginalFilename());
        Map<String, Object> result;
        // 验证文件是否为空
        if (file.isEmpty()) {
            LOG.warn("Attempted to upload empty file");
            result = new HashMap<>();
            result.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedFileType(contentType)) {
            LOG.warn("Invalid file type: {}", contentType);
            result = new HashMap<>();
            result.put("message", "不支持的文件类型");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            result = fileUploadService.uploadFile(file, categoryName, serviceName);
            LOG.info("Successfully uploaded file: {}", file.getOriginalFilename());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            LOG.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            result = new HashMap<>();
            result.put("message", "文件上传失败" + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取指定目录下的文件列表。
     *
     * @param tenantId     租户ID，通过请求参数 "tenantId" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @param uploadTime   上传文件的时间
     * @return 包含文件URL列表的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/list")
    public ResponseEntity<List<String>> getFilesInDirectory(
        @RequestParam(name = "tenantId") Long tenantId,
        @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName") String categoryName,
        @RequestParam(name = "uploadTime") String uploadTime
    ) {
        LOG.info(
            "Receiving file list request for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
            tenantId,
            categoryName,
            uploadTime,
            serviceName
        );
        try {
            List<String> fileUrls = fileUploadService.getFilesInDirectory(tenantId, categoryName, uploadTime, serviceName);
            return ResponseEntity.ok(fileUrls);
        } catch (Exception e) {
            LOG.error(
                "Failed to retrieve file list for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
                tenantId,
                categoryName,
                uploadTime,
                serviceName,
                e
            );
            return ResponseEntity.internalServerError().body(null);
        }
    }

    private boolean isAllowedFileType(String contentType) {
        for (String allowedType : Constants.ALLOWED_FILE_TYPES) {
            if (allowedType.equalsIgnoreCase(contentType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定文件在COS中的URL。
     *
     * @param key 文件的Key
     * @return 包含文件URL的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/getFileUrl")
    public ResponseEntity<String> getFileUrl(@RequestParam(name = "key", required = false) String key) {
        LOG.info("Receiving file list request for key: {}", key);
        try {
            String fileUrl = fileUploadService.getFileUrl(key);
            return ResponseEntity.ok().body(fileUrl);
        } catch (Exception e) {
            LOG.error("Failed to retrieve file list for key: {}", key, e);
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    @PostMapping("/cos/content")
    public ResponseEntity<String> readFileContent(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到读取文件内容请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            String content = cosService.readFileContent(request);
            if ("文件不存在".equals(content)) {
                LOG.warn("文件不存在: {}", request.getCosFileName());
                return ResponseEntity.notFound().build();
            }
            LOG.info("成功读取文件内容，文件名: {}", request.getCosFileName());
            return ResponseEntity.ok(content);
        } catch (Exception e) {
            LOG.error("读取文件内容失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body("读取文件内容失败: " + e.getMessage());
        }
    }

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件是否存在的布尔值
     */
    @PostMapping("/cos/exists")
    public ResponseEntity<Boolean> checkFileExists(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到检查文件是否存在请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            Boolean exists = cosService.checkFileExists(request);
            LOG.info("成功检查文件是否存在，文件名: {}, 存在: {}", request.getCosFileName(), exists);
            return ResponseEntity.ok(exists);
        } catch (Exception e) {
            LOG.error("检查文件是否存在失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body(false);
        }
    }

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件信息的JSON字符串
     */
    @PostMapping("/cos/info")
    public ResponseEntity<String> getFileInfo(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到获取文件信息请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            String fileInfo = cosService.getFileInfo(request);
            if (fileInfo.contains("\"error\"")) {
                LOG.warn("获取文件信息失败: {}", fileInfo);
                return ResponseEntity.notFound().build();
            }
            LOG.info("成功获取文件信息，文件名: {}", request.getCosFileName());
            return ResponseEntity.ok(fileInfo);
        } catch (Exception e) {
            LOG.error("获取文件信息失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body("{\"error\": \"" + e.getMessage() + "\"}");
        }
    }

    /**
     * 批量上传文件
     *
     * @param files        要上传的文件列表，通过请求参数 "files" 传递
     * @param serviceName  服务名称，用以生成bucket
     * @param categoryName 以"-"组成的类别
     * @return 批量上传结果，包含成功和失败的文件信息
     */
    @PostMapping("/batch/upload")
    public ResponseEntity<Map<String, Object>> batchUploadFiles(
        @Parameter(description = "要上传的文件列表", required = true)
        @RequestParam(name = "files") List<MultipartFile> files,
        @Parameter(description = "服务名称", required = true)
        @RequestParam(name = "serviceName") String serviceName,
        @Parameter(description = "类别名称")
        @RequestParam(name = "categoryName", required = false) String categoryName
    ) {
        LOG.info("接收到批量文件上传请求，文件数量: {}, 服务名称: {}, 类别: {}",
            files.size(), serviceName, categoryName);

        Map<String, Object> result = new HashMap<>();

        try {
            // 基本验证
            if (files.isEmpty()) {
                LOG.warn("批量上传文件列表为空");
                result.put("success", false);
                result.put("message", "文件列表不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (files.size() > 50) {
                LOG.warn("批量上传文件数量超过限制: {}", files.size());
                result.put("success", false);
                result.put("message", "批量上传文件数量不能超过50个");
                return ResponseEntity.badRequest().body(result);
            }

            // 执行批量上传
            Map<String, Object> uploadResult = fileUploadService.batchUploadFiles(files, categoryName, serviceName);

            LOG.info("批量文件上传完成，总数: {}, 成功: {}, 失败: {}",
                uploadResult.get("totalFiles"),
                uploadResult.get("successCount"),
                uploadResult.get("failureCount"));

            return ResponseEntity.ok(uploadResult);

        } catch (Exception e) {
            LOG.error("批量文件上传失败", e);
            result.put("success", false);
            result.put("message", "批量上传失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 异步批量上传文件
     *
     * @param files        要上传的文件列表
     * @param serviceName  服务名称
     * @param categoryName 类别名称
     * @return 异步上传任务信息
     */
    @PostMapping("/batch/upload/async")
    @Operation(summary = "异步批量上传文件", description = "异步方式批量上传文件，立即返回任务ID")
    public ResponseEntity<Map<String, Object>> batchUploadFilesAsync(
        @Parameter(description = "要上传的文件列表", required = true)
        @RequestParam(name = "files") List<MultipartFile> files,
        @Parameter(description = "服务名称", required = true)
        @RequestParam(name = "serviceName") String serviceName,
        @Parameter(description = "类别名称")
        @RequestParam(name = "categoryName", required = false) String categoryName
    ) {
        LOG.info("接收到异步批量文件上传请求，文件数量: {}, 服务名称: {}, 类别: {}",
            files.size(), serviceName, categoryName);

        Map<String, Object> result = new HashMap<>();

        try {
            // 基本验证
            if (files.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件列表不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            // 启动异步上传
            CompletableFuture<Map<String, Object>> future = fileUploadService.batchUploadFilesAsync(files, categoryName, serviceName);

            result.put("success", true);
            result.put("message", "异步批量上传任务已启动");
            result.put("taskId", "async-" + System.currentTimeMillis());
            result.put("totalFiles", files.size());
            result.put("status", "processing");

            // 异步处理结果
            future.thenAccept(uploadResult -> {
                LOG.info("异步批量上传完成: {}", uploadResult);
            }).exceptionally(throwable -> {
                LOG.error("异步批量上传失败", throwable);
                return null;
            });

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            LOG.error("启动异步批量上传失败", e);
            result.put("success", false);
            result.put("message", "启动异步上传失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取上传进度
     *
     * @param uploadId 上传任务ID
     * @return 上传进度信息
     */
    @GetMapping("/upload/progress")
    @Operation(summary = "获取上传进度", description = "根据上传任务ID获取上传进度")
    public ResponseEntity<Map<String, Object>> getUploadProgress(
        @Parameter(description = "上传任务ID", required = true)
        @RequestParam(name = "uploadId") String uploadId
    ) {
        LOG.info("获取上传进度请求，任务ID: {}", uploadId);
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> progress = fileUploadService.getUploadProgress(uploadId);

            if (progress == null) {
                result.put("success", false);
                result.put("message", "未找到指定的上传任务");
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(progress);

        } catch (Exception e) {
            LOG.error("获取上传进度失败，任务ID: {}", uploadId, e);
            result.put("success", false);
            result.put("message", "获取上传进度失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

}
