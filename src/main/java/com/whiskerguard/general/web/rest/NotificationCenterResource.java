package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.service.NotificationCenterService;
import com.whiskerguard.general.service.NotificationSendRecordService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.PaginationUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 通知中心管理
 *
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/api/notification-center")
@Tag(name = "notification-center", description = "通知中心 API")
public class NotificationCenterResource {

    private static final Logger log = LoggerFactory.getLogger(NotificationCenterResource.class);

    private static final String ENTITY_NAME = "notificationCenter";

    private final NotificationCenterService notificationCenterService;

    private final NotificationSendRecordService notificationSendRecordService;

    public NotificationCenterResource(
        NotificationCenterService notificationCenterService,
        NotificationSendRecordService notificationSendRecordService
    ) {
        this.notificationCenterService = notificationCenterService;
        this.notificationSendRecordService = notificationSendRecordService;
    }

    /**
     * 发送单个通知
     */
    @PostMapping("/send")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<NotificationRecordDTO> sendNotification(@Valid @RequestBody NotificationRequestDTO request) {
        log.debug("REST request to send notification: {}", request);

        if (request.getCategory() == null) {
            throw new BadRequestAlertException("通知分类不能为空", ENTITY_NAME, "category null");
        }

        if (request.getSubType() == null) {
            throw new BadRequestAlertException("通知子类型不能为空", ENTITY_NAME, "subtype null");
        }

        NotificationRecordDTO result = notificationCenterService.sendNotification(request);

        return ResponseEntity.ok(result);
    }

    /**
     * 批量发送通知
     */
    @PostMapping("/batch-send")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "批量发送成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<BatchNotificationResponseDTO> sendBatchNotification(@Valid @RequestBody BatchNotificationRequestDTO request) {
        log.debug("REST request to send batch notification: {}", request);

        if (request.getRecipientIds() == null || request.getRecipientIds().isEmpty()) {
            throw new BadRequestAlertException("接收者列表不能为空", ENTITY_NAME, "recipientsnull");
        }

        String batchId = notificationCenterService.sendBatchNotification(request);

        BatchNotificationResponseDTO response = new BatchNotificationResponseDTO();
        response.setBatchId(batchId);
        response.setStatus("PROCESSING");
        response.setMessage("批量通知已提交处理");
        response.setTotalCount(request.getRecipientIds().size());

        return ResponseEntity.ok(response);
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/system")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<NotificationRecordDTO> sendSystemNotification(@Valid @RequestBody SystemNotificationRequestDTO request) {
        log.debug("REST request to send system notification: {}", request);

        NotificationRecordDTO result = notificationCenterService.sendSystemNotification(request);

        return ResponseEntity.ok(result);
    }

    /**
     * 发送任务通知
     */
    @PostMapping("/task")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<NotificationRecordDTO> sendTaskNotification(@Valid @RequestBody TaskNotificationRequestDTO request) {
        log.debug("REST request to send task notification: {}", request);

        if (request.getBusinessId() == null || request.getBusinessId().trim().isEmpty()) {
            throw new BadRequestAlertException("业务ID不能为空", ENTITY_NAME, "businessidnull");
        }

        NotificationRecordDTO result = notificationCenterService.sendTaskNotification(request);

        return ResponseEntity.ok(result);
    }

    /**
     * 发送用户通知
     */
    @PostMapping("/user")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "发送成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<NotificationRecordDTO> sendUserNotification(@Valid @RequestBody UserNotificationRequestDTO request) {
        log.debug("REST request to send user notification: {}", request);

        if (request.getUserId() == null) {
            throw new BadRequestAlertException("用户ID不能为空", ENTITY_NAME, "useridnull");
        }

        NotificationRecordDTO result = notificationCenterService.sendUserNotification(request);

        return ResponseEntity.ok(result);
    }

    /**
     * 取消计划通知
     */
    @PutMapping("/{id}/cancel")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "取消成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "404", description = "通知不存在"),
        }
    )
    public ResponseEntity<Void> cancelScheduledNotification(@PathVariable Long id) {
        log.debug("REST request to cancel scheduled notification: {}", id);

        notificationCenterService.cancelScheduledNotification(id);

        return ResponseEntity.ok().build();
    }

    /**
     * 重试失败通知
     */
    @PostMapping("/retry-failed")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "重试成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Void> retryFailedNotifications() {
        log.debug("REST request to retry failed notifications");

        notificationCenterService.retryFailedNotifications();

        return ResponseEntity.ok().build();
    }

    /**
     * 获取批量通知状态
     */
    @GetMapping("/batch/{batchId}/status")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "获取成功"), @ApiResponse(responseCode = "404", description = "批次不存在"),
        }
    )
    public ResponseEntity<BatchNotificationStatusDTO> getBatchNotificationStatus(@PathVariable String batchId) {
        log.debug("REST request to get batch notification status: {}", batchId);

        BatchNotificationStatusDTO status = notificationCenterService.getBatchNotificationStatus(batchId);

        return ResponseEntity.ok(status);
    }

    /**
     * 标记通知为已读
     */
    @PutMapping("/{id}/read")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "标记成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "404", description = "通知不存在"),
        }
    )
    public ResponseEntity<Void> markAsRead(@PathVariable Long id, @RequestParam Long userId) {
        log.debug("REST request to mark notification as read: notificationId={}, userId={}", id, userId);

        notificationCenterService.markAsRead(id, userId);

        return ResponseEntity.ok().build();
    }

    /**
     * 批量标记通知为已读
     */
    @PutMapping("/batch-read")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "批量标记成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
        }
    )
    public ResponseEntity<Void> batchMarkAsRead(@RequestBody List<Long> notificationIds, @RequestParam Long userId) {
        log.debug("REST request to batch mark notifications as read: notificationIds={}, userId={}", notificationIds, userId);

        if (notificationIds == null || notificationIds.isEmpty()) {
            throw new BadRequestAlertException("通知ID列表不能为空", ENTITY_NAME, "notificationidsnull");
        }
        notificationCenterService.batchMarkAsRead(notificationIds, userId);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据用户ID和接收者类型获取通知发送记录的分页列表
     *
     * @param requestDTO 包含用户ID和接收者类型的请求对象
     * @param pageable   分页信息
     * @return 通知发送记录列表
     */
    @PostMapping("/records/category")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Page<NotificationSendRecordDTO>> getNotificationSendRecordsByUserAndCategory(
        @Valid @RequestBody UserCategoryRequestDTO requestDTO,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        log.debug("REST request to get notification send records by user and category: {}", requestDTO);
        try {
            Page<NotificationSendRecordDTO> page = notificationSendRecordService.findByUserIdAndCategory(requestDTO.getCategoryName(), pageable);
            HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
            return ResponseEntity.ok().headers(headers).body(page);
        } catch (IllegalArgumentException e) {
            throw new BadRequestAlertException("无效的接收者类型: " + requestDTO.getCategoryName(), ENTITY_NAME, "type Invalid");
        }
    }

    /**
     * 获取通知详情
     *
     * @param id 通知ID
     * @return 通知详情
     */
    @GetMapping("/notification-record/{id}")
    @Operation(summary = "获取通知详情", description = "根据通知ID获取通知的详细信息")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "404", description = "通知不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<NotificationRecordDTO> getNotificationRecord(@PathVariable Long id) {
        log.debug("REST request to get NotificationRecord : {}", id);

        Optional<NotificationRecordDTO> notificationRecordDTO = notificationCenterService.findNotificationRecord(id);

        return notificationRecordDTO
            .map(ResponseEntity::ok)
            .orElseThrow(() -> new BadRequestAlertException("通知不存在", ENTITY_NAME, "idnotfound"));
    }

    /**
     * 统计当前用户未读消息数量
     *
     * @param userId 用户ID
     * @param recipientType 接收者类型（可选，默认为USER）
     * @return 未读消息统计信息
     */
    @GetMapping("/unread-count")
    @Operation(summary = "统计用户未读消息数量", description = "统计指定用户的未读消息总数和按分类统计的未读消息数量")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "统计成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Map<String, Object>> getUnreadMessageCount(
        @RequestParam Long userId,
        @RequestParam(defaultValue = "USER") String recipientType) {
        log.debug("REST request to get unread message count for user: {}, recipientType: {}", userId, recipientType);

        if (userId == null) {
            throw new BadRequestAlertException("用户ID不能为空", ENTITY_NAME, "useridnull");
        }

        try {
            RecipientType type = RecipientType.valueOf(recipientType.toUpperCase());

            // 获取总的未读消息数量
            Long totalUnreadCount = notificationCenterService.countUnreadMessages(userId, type);

            // 获取按分类统计的未读消息数量
            Map<NotificationCategory, Long> unreadByCategory = notificationCenterService.countUnreadMessagesByCategory(userId, type);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("userId", userId);
            response.put("recipientType", recipientType);
            response.put("totalUnreadCount", totalUnreadCount);

            // 将枚举类型转换为字符串键
            Map<String, Long> categoryMap = new HashMap<>();
            for (Map.Entry<NotificationCategory, Long> entry : unreadByCategory.entrySet()) {
                categoryMap.put(entry.getKey().name(), entry.getValue());
            }
            response.put("unreadByCategory", categoryMap);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            throw new BadRequestAlertException("无效的接收者类型: " + recipientType, ENTITY_NAME, "invalidRecipientType");
        }
    }

    /**
     * 统计当前用户指定分类的未读消息数量
     *
     * @param userId 用户ID
     * @param category 通知分类
     * @param recipientType 接收者类型（可选，默认为USER）
     * @return 指定分类的未读消息数量
     */
    @GetMapping("/unread-count/category")
    @Operation(summary = "统计用户指定分类的未读消息数量", description = "统计指定用户在特定分类下的未读消息数量")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "统计成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Map<String, Object>> getUnreadMessageCountByCategory(
        @RequestParam Long userId,
        @RequestParam String category,
        @RequestParam(defaultValue = "USER") String recipientType) {
        log.debug("REST request to get unread message count for user: {}, category: {}, recipientType: {}",
            userId, category, recipientType);

        if (userId == null) {
            throw new BadRequestAlertException("用户ID不能为空", ENTITY_NAME, "useridnull");
        }

        if (category == null || category.trim().isEmpty()) {
            throw new BadRequestAlertException("通知分类不能为空", ENTITY_NAME, "categorynull");
        }

        try {
            RecipientType type = RecipientType.valueOf(recipientType.toUpperCase());
            NotificationCategory notificationCategory = NotificationCategory.valueOf(category.toUpperCase());

            // 获取指定分类的未读消息数量
            Long unreadCount = notificationSendRecordService.countUnreadMessagesByCategory(userId, type, notificationCategory);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("userId", userId);
            response.put("category", category);
            response.put("recipientType", recipientType);
            response.put("unreadCount", unreadCount);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            throw new BadRequestAlertException("无效的参数: " + e.getMessage(), ENTITY_NAME, "invalidParameter");
        }
    }
}
