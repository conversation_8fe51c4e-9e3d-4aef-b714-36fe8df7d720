package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.service.UserBindingTemplateService;
import com.whiskerguard.general.service.dto.UserBindingTemplateDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tech.jhipster.web.util.HeaderUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * 用户绑定消息模板管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/3
 */
@RestController
@RequestMapping("/api/user/binding/templates")
public class UserBindingTemplateResource {

    private static final Logger LOG = LoggerFactory.getLogger(UserBindingTemplateResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceUserBindingTemplate";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final UserBindingTemplateService userBindingTemplateService;

    public UserBindingTemplateResource(
        UserBindingTemplateService userBindingTemplateService) {
        this.userBindingTemplateService = userBindingTemplateService;
    }

    /**
     * 方法名称：createUserBindingTemplate
     * 描述：创建用户绑定消息模板。
     *
     * @param userBindingTemplateDTO 用户绑定消息模板DTO
     * @return 用户绑定消息模板DTO
     * @throws URISyntaxException URI语法异常
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<UserBindingTemplateDTO> createUserBindingTemplate(
        @Valid @RequestBody UserBindingTemplateDTO userBindingTemplateDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save UserBindingTemplate : {}", userBindingTemplateDTO);
        if (userBindingTemplateDTO.getId() != null) {
            throw new BadRequestAlertException("新建用户绑定模板不能指定ID", ENTITY_NAME, "id exists");
        }
        userBindingTemplateDTO = userBindingTemplateService.save(userBindingTemplateDTO);
        return ResponseEntity.created(new URI("/api/user/binding/templates/" + userBindingTemplateDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, userBindingTemplateDTO.getId().toString()))
            .body(userBindingTemplateDTO);
    }

    /**
     * 方法名称：getAllUserBindingTemplates
     * 描述：获取所有用户绑定消息模板。
     *
     * @return 用户绑定消息模板DTO列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<List<UserBindingTemplateDTO>> getAllUserBindingTemplates() {
        LOG.debug("REST request to get a page of UserBindingTemplates");
        return ResponseEntity.ok().body(userBindingTemplateService.findAll());
    }

}
