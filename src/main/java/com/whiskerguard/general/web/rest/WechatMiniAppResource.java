package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.service.WechatMiniAppService;
import com.whiskerguard.general.service.dto.BatchSubscribeMessageRequest;
import com.whiskerguard.general.service.dto.SubscribeMessageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 微信小程序推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/2
 */
@RestController
@RequestMapping("/api/wechat/miniapp")
public class WechatMiniAppResource {

    private static final Logger log = LoggerFactory.getLogger(WechatMiniAppResource.class);

    private final WechatMiniAppService wechatMiniAppService;

    public WechatMiniAppResource(WechatMiniAppService wechatMiniAppService) {
        this.wechatMiniAppService = wechatMiniAppService;
    }

    /**
     * 发送订阅消息
     */
    @PostMapping("/push/subscribe-message")
    public ResponseEntity<NotificationResponse> sendSubscribeMessage(@Valid @RequestBody SubscribeMessageRequest request) {
        log.info("发送小程序订阅消息: openId={}, templateId={}", request.getOpenId(), request.getTemplateId());
        NotificationResponse response = wechatMiniAppService.sendSubscribeMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 批量发送订阅消息
     */
    @PostMapping("/push/batch-subscribe-message")
    public ResponseEntity<NotificationResponse> sendBatchSubscribeMessage(@Valid @RequestBody BatchSubscribeMessageRequest request) {
        log.info("批量发送小程序订阅消息: openIds={}, templateId={}", request.getOpenIds().size(), request.getTemplateId());
        NotificationResponse response = wechatMiniAppService.sendBatchSubscribeMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 通过code获取用户信息
     */
    @GetMapping("/auth/code2session")
    public ResponseEntity<Map<String, Object>> code2Session(@RequestParam @NotBlank String code) {
        log.info("小程序登录凭证校验: code={}", code);
        Map<String, Object> result = wechatMiniAppService.code2Session(code);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取AccessToken
     */
    @GetMapping("/access-token")
    public ResponseEntity<Map<String, Object>> getAccessToken() {
        log.info("获取小程序AccessToken");

        String accessToken = wechatMiniAppService.getAccessToken();
        Map<String, Object> result = Map.of(
            "accessToken", accessToken != null ? accessToken : "",
            "success", accessToken != null
        );
        return ResponseEntity.ok(result);
    }

}
