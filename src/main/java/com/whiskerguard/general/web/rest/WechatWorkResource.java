package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatWorkRequest;
import com.whiskerguard.general.service.WechatWorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 企业微信REST API控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
@RestController
@RequestMapping("/api/wechat-work")
@Tag(name = "企业微信API", description = "企业微信消息推送和用户管理API")
public class WechatWorkResource {

    private static final Logger log = LoggerFactory.getLogger(WechatWorkResource.class);

    private final WechatWorkService wechatWorkService;

    public WechatWorkResource(WechatWorkService wechatWorkService) {
        this.wechatWorkService = wechatWorkService;
    }

    /**
     * 发送企业微信消息
     */
    @PostMapping("/send-message")
    @Operation(summary = "发送企业微信消息", description = "向企业微信用户发送各种类型的消息")
    public ResponseEntity<NotificationResponse> sendMessage(
        @Valid @RequestBody WechatWorkRequest request) {
        log.debug("发送企业微信消息请求: {}", request);

        NotificationResponse response = wechatWorkService.sendMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 发送文本消息
     */
    @PostMapping("/send-text")
    @Operation(summary = "发送文本消息", description = "向企业微信用户发送文本消息")
    public ResponseEntity<NotificationResponse> sendTextMessage(
        @Parameter(description = "接收用户ID，多个用|分隔") @RequestParam String toUser,
        @Parameter(description = "消息内容") @RequestParam String content) {
        log.debug("发送企业微信文本消息，用户: {}, 内容: {}", toUser, content);

        NotificationResponse response = wechatWorkService.sendTextMessage(toUser, content);
        return ResponseEntity.ok(response);
    }


    /**
     * 批量发送消息
     */
    @PostMapping("/batch-send")
    @Operation(summary = "批量发送消息", description = "批量向企业微信用户发送消息")
    public ResponseEntity<List<NotificationResponse>> batchSendMessage(
        @Valid @RequestBody List<WechatWorkRequest> requests) {
        log.debug("批量发送企业微信消息，数量: {}", requests.size());

        List<NotificationResponse> responses = wechatWorkService.batchSendMessage(requests);
        return ResponseEntity.ok(responses);
    }

    /**
     * 获取访问令牌
     */
    @GetMapping("/access-token")
    @Operation(summary = "获取访问令牌", description = "获取企业微信访问令牌")
    public ResponseEntity<Map<String, String>> getAccessToken() {
        log.debug("获取企业微信访问令牌");

        String accessToken = wechatWorkService.getAccessToken();
        return ResponseEntity.ok(Map.of("access_token", accessToken != null ? accessToken : ""));
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取企业微信用户信息")
    public ResponseEntity<Map<String, Object>> getUserInfo(
        @Parameter(description = "企业微信用户ID") @PathVariable String userId) {
        log.debug("获取企业微信用户信息，用户ID: {}", userId);

        Map<String, Object> userInfo = wechatWorkService.getUserInfo(userId);
        return ResponseEntity.ok(userInfo);
    }

    /**
     * 获取部门用户列表
     */
    @GetMapping("/department/{departmentId}/users")
    @Operation(summary = "获取部门用户列表", description = "获取指定部门的用户列表")
    public ResponseEntity<List<Map<String, Object>>> getDepartmentUsers(
        @Parameter(description = "部门ID") @PathVariable String departmentId,
        @Parameter(description = "是否递归获取子部门用户") @RequestParam(defaultValue = "false") boolean fetchChild) {
        log.debug("获取部门用户列表，部门ID: {}, 递归: {}", departmentId, fetchChild);

        List<Map<String, Object>> users = wechatWorkService.getDepartmentUsers(departmentId, fetchChild);
        return ResponseEntity.ok(users);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    @Operation(summary = "获取部门列表", description = "获取企业微信部门列表")
    public ResponseEntity<List<Map<String, Object>>> getDepartments(
        @Parameter(description = "部门ID，不传获取全量组织架构") @RequestParam(required = false) String departmentId) {
        log.debug("获取部门列表，部门ID: {}", departmentId);

        List<Map<String, Object>> departments = wechatWorkService.getDepartments(departmentId);
        return ResponseEntity.ok(departments);
    }

    /**
     * 根据手机号获取用户ID
     */
    @GetMapping("/user-id/by-mobile")
    @Operation(summary = "根据手机号获取用户ID", description = "根据手机号获取企业微信用户ID")
    public ResponseEntity<Map<String, String>> getUserIdByMobile(
        @Parameter(description = "手机号") @RequestParam String mobile) {
        log.debug("根据手机号获取企业微信用户ID，手机号: {}", mobile);

        String userId = wechatWorkService.getUserIdByMobile(mobile);
        return ResponseEntity.ok(Map.of("userid", userId != null ? userId : ""));
    }

    /**
     * 根据邮箱获取用户ID
     */
    @GetMapping("/user-id/by-email")
    @Operation(summary = "根据邮箱获取用户ID", description = "根据邮箱获取企业微信用户ID")
    public ResponseEntity<Map<String, String>> getUserIdByEmail(
        @Parameter(description = "邮箱") @RequestParam String email) {
        log.debug("根据邮箱获取企业微信用户ID，邮箱: {}", email);

        String userId = wechatWorkService.getUserIdByEmail(email);
        return ResponseEntity.ok(Map.of("userid", userId != null ? userId : ""));
    }

    /**
     * 根据手机号获取用户信息
     */
    @GetMapping("/user/mobile/{mobile}")
    @Operation(summary = "根据手机号获取用户信息", description = "根据手机号查询企业微信用户信息")
    public ResponseEntity<Map<String, Object>> getUserInfoByMobile(
        @Parameter(description = "手机号") @PathVariable String mobile) {
        log.debug("根据手机号获取企业微信用户信息，手机号: {}", mobile);

        Map<String, Object> userInfo = wechatWorkService.getUserInfoByMobile(mobile);
        return ResponseEntity.ok(userInfo);
    }

    /**
     * 获取群聊列表
     */
    @GetMapping("/group-chats")
    @Operation(summary = "获取群聊列表", description = "获取企业微信群聊列表")
    public ResponseEntity<Map<String, Object>> getGroupChatList(
        @Parameter(description = "群聊状态过滤：0-正常，1-跟进人离职，2-离职继承中，3-已继承") @RequestParam(required = false) Integer status,
        @Parameter(description = "群主过滤：群主的userid") @RequestParam(required = false) String owner,
        @Parameter(description = "分页游标") @RequestParam(required = false) String cursor,
        @Parameter(description = "返回的最大记录数，最大1000") @RequestParam(defaultValue = "100") Integer limit) {
        log.debug("获取企业微信群聊列表，状态: {}, 群主: {}, 游标: {}, 限制: {}", status, owner, cursor, limit);

        Map<String, Object> result = wechatWorkService.getGroupChatList(status, owner, cursor, limit);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 发送群聊消息
     */
    @PostMapping("/group-chat/{chatId}/send-message")
    @Operation(summary = "发送群聊消息", description = "向指定群聊发送消息")
    public ResponseEntity<NotificationResponse> sendGroupChatMessage(
        @Parameter(description = "群聊ID") @PathVariable String chatId,
        @Parameter(description = "消息类型") @RequestParam String msgType,
        @Parameter(description = "消息内容") @RequestBody Map<String, Object> content) {
        log.debug("发送企业微信群聊消息，群聊ID: {}, 消息类型: {}", chatId, msgType);

        NotificationResponse response = wechatWorkService.sendGroupChatMessage(chatId, msgType, content);
        return ResponseEntity.ok(response);
    }

}
