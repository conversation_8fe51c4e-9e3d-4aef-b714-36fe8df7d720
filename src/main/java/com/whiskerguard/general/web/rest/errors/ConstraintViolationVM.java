package com.whiskerguard.general.web.rest.errors;

import java.io.Serializable;

/**
 * 约束违反视图模型
 * 用于表示Jakarta Bean Validation约束违反的详细信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/2
 */
public class ConstraintViolationVM implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性路径（字段名）
     */
    private final String propertyPath;

    /**
     * 约束违反消息
     */
    private final String message;

    /**
     * 无效的值
     */
    private final String invalidValue;

    /**
     * 构造函数
     *
     * @param propertyPath 属性路径
     * @param message      约束违反消息
     * @param invalidValue 无效的值
     */
    public ConstraintViolationVM(String propertyPath, String message, String invalidValue) {
        this.propertyPath = propertyPath;
        this.message = message;
        this.invalidValue = invalidValue;
    }

    /**
     * 获取属性路径
     *
     * @return 属性路径
     */
    public String getPropertyPath() {
        return propertyPath;
    }

    /**
     * 获取约束违反消息
     *
     * @return 约束违反消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取无效的值
     *
     * @return 无效的值
     */
    public String getInvalidValue() {
        return invalidValue;
    }

    @Override
    public String toString() {
        return "ConstraintViolationVM{" +
            "propertyPath='" + propertyPath + '\'' +
            ", message='" + message + '\'' +
            ", invalidValue='" + invalidValue + '\'' +
            '}';
    }
}
