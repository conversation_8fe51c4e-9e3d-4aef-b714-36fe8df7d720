<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity UserBindingTemplate.
    -->
    <changeSet id="20250703025248-1" author="jhipster">
        <createTable tableName="user_binding_template" remarks="用户绑定消息模板实体">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID">
                <constraints nullable="true" />
            </column>
            <column name="employee_id" type="bigint" remarks="用户ID">
                <constraints nullable="false" />
            </column>
            <column name="template_id" type="varchar(64)" remarks="模板ID">
                <constraints nullable="false" />
            </column>
            <column name="template_name" type="varchar(32)" remarks="模板名称">
                <constraints nullable="false" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展信息（JSON格式）">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="版本号（乐观锁）">
                <constraints nullable="true" />
            </column>
            <column name="created_by" type="varchar(64)" remarks="创建人">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(64)" remarks="更新人">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="是否删除">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="user_binding_template" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="user_binding_template" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250703025248-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/user_binding_template.csv"
                  separator=";"
                  tableName="user_binding_template"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="employee_id" type="numeric"/>
            <column name="template_id" type="string"/>
            <column name="template_name" type="string"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
