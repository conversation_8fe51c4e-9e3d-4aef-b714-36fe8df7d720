<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="now()" dbms="mysql"/>
    <property name="floatType" value="float" dbms="mysql"/>
    <property name="clobType" value="clob" dbms="mysql"/>
    <property name="blobType" value="longblob" dbms="mysql"/>
    <property name="uuidType" value="varchar(36)" dbms="mysql"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql"/>
    <property name="timeType" value="time(6)" dbms="mysql"/>

    <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250523091435_added_entity_SignatureDocument.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250609084418_added_entity_SensitiveWord.xml" relativeToChangelogFile="false"/>

  <include file="config/liquibase/changelog/20250610000001_added_entity_Company.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250610000002_added_entity_CompanyContact.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250610000003_added_entity_CompanyRisk.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250610000004_added_entity_CompanyChangeRecord.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250610000005_added_entity_CompanyDishonestPerson.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250610000006_added_entity_CompanyCaseFiling.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250611000001_fix_sequence_generator.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250611112500_update_entity_id_generation.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250611132500_fix_mysql_auto_increment.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250611112900_fix_auto_increment.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/20250623112934_added_entity_NotificationRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250623112935_added_entity_NotificationTemplate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250623112936_added_entity_UserNotificationPreference.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250623112937_added_entity_NotificationSendRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250624134316_added_entity_UserWechatBinding.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250703025248_added_entity_UserBindingTemplate.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
    <include file="config/liquibase/changelog/20250623112934_added_entity_constraints_NotificationRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250623112937_added_entity_constraints_NotificationSendRecord.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>
