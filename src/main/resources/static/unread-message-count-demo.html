<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>未读消息统计演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.secondary {
            background-color: #6c757d;
        }
        button.secondary:hover {
            background-color: #545b62;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stats-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .stats-card .count {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>未读消息统计演示</h1>

        <!-- 统计总未读消息数量 -->
        <div class="section">
            <h3>1. 统计用户未读消息总数</h3>
            <div class="form-group">
                <label for="userId1">用户ID:</label>
                <input type="number" id="userId1" placeholder="输入用户ID" value="1">
            </div>
            <div class="form-group">
                <label for="recipientType1">接收者类型:</label>
                <select id="recipientType1">
                    <option value="USER">用户 (USER)</option>
                    <option value="ROLE">角色 (ROLE)</option>
                    <option value="DEPARTMENT">部门 (DEPARTMENT)</option>
                    <option value="ALL">全部 (ALL)</option>
                </select>
            </div>
            <button onclick="getTotalUnreadCount()">获取总未读数量</button>
            <button class="secondary" onclick="clearTotalResult()">清空结果</button>

            <div id="totalUnreadResult"></div>
        </div>

        <!-- 统计指定分类的未读消息数量 -->
        <div class="section">
            <h3>2. 统计用户指定分类的未读消息数量</h3>
            <div class="form-group">
                <label for="userId2">用户ID:</label>
                <input type="number" id="userId2" placeholder="输入用户ID" value="1">
            </div>
            <div class="form-group">
                <label for="category">通知分类:</label>
                <select id="category">
                    <option value="SYSTEM">系统通知 (SYSTEM)</option>
                    <option value="TASK">任务通知 (TASK)</option>
                    <option value="USER">用户通知 (USER)</option>
                    <option value="BUSINESS">企业通知 (BUSINESS)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="recipientType2">接收者类型:</label>
                <select id="recipientType2">
                    <option value="USER">用户 (USER)</option>
                    <option value="ROLE">角色 (ROLE)</option>
                    <option value="DEPARTMENT">部门 (DEPARTMENT)</option>
                    <option value="ALL">全部 (ALL)</option>
                </select>
            </div>
            <button onclick="getCategoryUnreadCount()">获取分类未读数量</button>
            <button class="secondary" onclick="clearCategoryResult()">清空结果</button>

            <div id="categoryUnreadResult"></div>
        </div>

        <!-- 使用说明 -->
        <div class="section">
            <h3>使用说明</h3>
            <div class="note">
                <p><strong>API端点：</strong></p>
                <ul>
                    <li><code>GET /api/notification-center/unread-count</code> - 获取用户未读消息总数和按分类统计</li>
                    <li><code>GET /api/notification-center/unread-count/category</code> - 获取用户指定分类的未读消息数量</li>
                </ul>
                <p><strong>参数说明：</strong></p>
                <ul>
                    <li><strong>userId</strong>: 用户ID（必需）</li>
                    <li><strong>recipientType</strong>: 接收者类型（可选，默认为USER）</li>
                    <li><strong>category</strong>: 通知分类（第二个API必需）</li>
                </ul>
                <p><strong>注意事项：</strong></p>
                <ul>
                    <li>统计基于数据库中的实际数据</li>
                    <li>只统计状态为SENT且readTime为空的记录</li>
                    <li>支持按不同接收者类型和通知分类进行统计</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 获取总未读消息数量
        function getTotalUnreadCount() {
            const userId = document.getElementById('userId1').value;
            const recipientType = document.getElementById('recipientType1').value;

            if (!userId) {
                showTotalResult('请输入用户ID', 'error');
                return;
            }

            const params = new URLSearchParams({
                userId: userId,
                recipientType: recipientType
            });

            fetch(`/api/notification-center/unread-count?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.totalUnreadCount !== undefined) {
                        let result = `未读消息统计成功！\n\n`;
                        result += `用户ID: ${data.userId}\n`;
                        result += `接收者类型: ${data.recipientType}\n`;
                        result += `总未读消息数量: ${data.totalUnreadCount}\n\n`;
                        result += `按分类统计:\n`;

                        const categories = data.unreadByCategory || {};
                        for (const [category, count] of Object.entries(categories)) {
                            result += `- ${category}: ${count}条\n`;
                        }

                        showTotalResult(result, 'success');

                        // 显示统计卡片
                        showStatsCards(data);
                    } else {
                        showTotalResult(`获取失败: ${data.message || '未知错误'}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showTotalResult(`请求失败: ${error.message}`, 'error');
                });
        }

        // 获取指定分类的未读消息数量
        function getCategoryUnreadCount() {
            const userId = document.getElementById('userId2').value;
            const category = document.getElementById('category').value;
            const recipientType = document.getElementById('recipientType2').value;

            if (!userId) {
                showCategoryResult('请输入用户ID', 'error');
                return;
            }

            const params = new URLSearchParams({
                userId: userId,
                category: category,
                recipientType: recipientType
            });

            fetch(`/api/notification-center/unread-count/category?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.unreadCount !== undefined) {
                        let result = `分类未读消息统计成功！\n\n`;
                        result += `用户ID: ${data.userId}\n`;
                        result += `通知分类: ${data.category}\n`;
                        result += `接收者类型: ${data.recipientType}\n`;
                        result += `未读消息数量: ${data.unreadCount}条\n`;

                        showCategoryResult(result, 'success');
                    } else {
                        showCategoryResult(`获取失败: ${data.message || '未知错误'}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showCategoryResult(`请求失败: ${error.message}`, 'error');
                });
        }

        // 显示统计卡片
        function showStatsCards(data) {
            const categories = data.unreadByCategory || {};
            let cardsHtml = '<div class="stats-grid">';

            // 总数卡片
            cardsHtml += `
                <div class="stats-card">
                    <h4>总未读消息</h4>
                    <div class="count">${data.totalUnreadCount}</div>
                </div>
            `;

            // 各分类卡片
            for (const [category, count] of Object.entries(categories)) {
                const categoryNames = {
                    'SYSTEM': '系统通知',
                    'TASK': '任务通知',
                    'USER': '用户通知',
                    'BUSINESS': '企业通知'
                };

                cardsHtml += `
                    <div class="stats-card">
                        <h4>${categoryNames[category] || category}</h4>
                        <div class="count">${count}</div>
                    </div>
                `;
            }

            cardsHtml += '</div>';

            const resultDiv = document.getElementById('totalUnreadResult');
            resultDiv.innerHTML += cardsHtml;
        }

        // 显示总数结果
        function showTotalResult(message, type) {
            const resultDiv = document.getElementById('totalUnreadResult');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 显示分类结果
        function showCategoryResult(message, type) {
            const resultDiv = document.getElementById('categoryUnreadResult');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 清空总数结果
        function clearTotalResult() {
            document.getElementById('totalUnreadResult').innerHTML = '';
        }

        // 清空分类结果
        function clearCategoryResult() {
            document.getElementById('categoryUnreadResult').innerHTML = '';
        }
    </script>
</body>
</html>
