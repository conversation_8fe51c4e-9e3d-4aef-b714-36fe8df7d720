package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class UserBindingTemplateAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserBindingTemplateAllPropertiesEquals(UserBindingTemplate expected, UserBindingTemplate actual) {
        assertUserBindingTemplateAutoGeneratedPropertiesEquals(expected, actual);
        assertUserBindingTemplateAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserBindingTemplateAllUpdatablePropertiesEquals(UserBindingTemplate expected, UserBindingTemplate actual) {
        assertUserBindingTemplateUpdatableFieldsEquals(expected, actual);
        assertUserBindingTemplateUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserBindingTemplateAutoGeneratedPropertiesEquals(UserBindingTemplate expected, UserBindingTemplate actual) {
        assertThat(actual)
            .as("Verify UserBindingTemplate auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserBindingTemplateUpdatableFieldsEquals(UserBindingTemplate expected, UserBindingTemplate actual) {
        assertThat(actual)
            .as("Verify UserBindingTemplate relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getTemplateId()).as("check templateId").isEqualTo(expected.getTemplateId()))
            .satisfies(a -> assertThat(a.getTemplateName()).as("check templateName").isEqualTo(expected.getTemplateName()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserBindingTemplateUpdatableRelationshipsEquals(UserBindingTemplate expected, UserBindingTemplate actual) {
        // empty method
    }
}
