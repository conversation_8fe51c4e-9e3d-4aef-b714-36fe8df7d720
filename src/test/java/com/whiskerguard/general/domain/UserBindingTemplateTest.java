package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.UserBindingTemplateTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserBindingTemplateTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserBindingTemplate.class);
        UserBindingTemplate userBindingTemplate1 = getUserBindingTemplateSample1();
        UserBindingTemplate userBindingTemplate2 = new UserBindingTemplate();
        assertThat(userBindingTemplate1).isNotEqualTo(userBindingTemplate2);

        userBindingTemplate2.setId(userBindingTemplate1.getId());
        assertThat(userBindingTemplate1).isEqualTo(userBindingTemplate2);

        userBindingTemplate2 = getUserBindingTemplateSample2();
        assertThat(userBindingTemplate1).isNotEqualTo(userBindingTemplate2);
    }
}
