package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class UserBindingTemplateTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static UserBindingTemplate getUserBindingTemplateSample1() {
        return new UserBindingTemplate()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .templateId("templateId1")
            .templateName("templateName1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static UserBindingTemplate getUserBindingTemplateSample2() {
        return new UserBindingTemplate()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .templateId("templateId2")
            .templateName("templateName2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static UserBindingTemplate getUserBindingTemplateRandomSampleGenerator() {
        return new UserBindingTemplate()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .templateId(UUID.randomUUID().toString())
            .templateName(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
