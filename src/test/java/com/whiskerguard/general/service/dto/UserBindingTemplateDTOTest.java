package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserBindingTemplateDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserBindingTemplateDTO.class);
        UserBindingTemplateDTO userBindingTemplateDTO1 = new UserBindingTemplateDTO();
        userBindingTemplateDTO1.setId(1L);
        UserBindingTemplateDTO userBindingTemplateDTO2 = new UserBindingTemplateDTO();
        assertThat(userBindingTemplateDTO1).isNotEqualTo(userBindingTemplateDTO2);
        userBindingTemplateDTO2.setId(userBindingTemplateDTO1.getId());
        assertThat(userBindingTemplateDTO1).isEqualTo(userBindingTemplateDTO2);
        userBindingTemplateDTO2.setId(2L);
        assertThat(userBindingTemplateDTO1).isNotEqualTo(userBindingTemplateDTO2);
        userBindingTemplateDTO1.setId(null);
        assertThat(userBindingTemplateDTO1).isNotEqualTo(userBindingTemplateDTO2);
    }
}
