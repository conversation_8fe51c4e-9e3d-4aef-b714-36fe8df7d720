package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.UserBindingTemplateAsserts.*;
import static com.whiskerguard.general.domain.UserBindingTemplateTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class UserBindingTemplateMapperTest {

    private UserBindingTemplateMapper userBindingTemplateMapper;

    @BeforeEach
    void setUp() {
        userBindingTemplateMapper = new UserBindingTemplateMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getUserBindingTemplateSample1();
        var actual = userBindingTemplateMapper.toEntity(userBindingTemplateMapper.toDto(expected));
        assertUserBindingTemplateAllPropertiesEquals(expected, actual);
    }
}
