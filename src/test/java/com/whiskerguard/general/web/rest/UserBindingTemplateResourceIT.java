package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.UserBindingTemplateAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.UserBindingTemplate;
import com.whiskerguard.general.repository.UserBindingTemplateRepository;
import com.whiskerguard.general.service.dto.UserBindingTemplateDTO;
import com.whiskerguard.general.service.mapper.UserBindingTemplateMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link UserBindingTemplateResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class UserBindingTemplateResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_TEMPLATE_ID = "AAAAAAAAAA";
    private static final String UPDATED_TEMPLATE_ID = "BBBBBBBBBB";

    private static final String DEFAULT_TEMPLATE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_TEMPLATE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/user-binding-templates";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private UserBindingTemplateRepository userBindingTemplateRepository;

    @Autowired
    private UserBindingTemplateMapper userBindingTemplateMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUserBindingTemplateMockMvc;

    private UserBindingTemplate userBindingTemplate;

    private UserBindingTemplate insertedUserBindingTemplate;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserBindingTemplate createEntity() {
        return new UserBindingTemplate()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .templateId(DEFAULT_TEMPLATE_ID)
            .templateName(DEFAULT_TEMPLATE_NAME)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserBindingTemplate createUpdatedEntity() {
        return new UserBindingTemplate()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .templateId(UPDATED_TEMPLATE_ID)
            .templateName(UPDATED_TEMPLATE_NAME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        userBindingTemplate = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUserBindingTemplate != null) {
            userBindingTemplateRepository.delete(insertedUserBindingTemplate);
            insertedUserBindingTemplate = null;
        }
    }

    @Test
    @Transactional
    void createUserBindingTemplate() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);
        var returnedUserBindingTemplateDTO = om.readValue(
            restUserBindingTemplateMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            UserBindingTemplateDTO.class
        );

        // Validate the UserBindingTemplate in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUserBindingTemplate = userBindingTemplateMapper.toEntity(returnedUserBindingTemplateDTO);
        assertUserBindingTemplateUpdatableFieldsEquals(
            returnedUserBindingTemplate,
            getPersistedUserBindingTemplate(returnedUserBindingTemplate)
        );

        insertedUserBindingTemplate = returnedUserBindingTemplate;
    }

    @Test
    @Transactional
    void createUserBindingTemplateWithExistingId() throws Exception {
        // Create the UserBindingTemplate with an existing ID
        userBindingTemplate.setId(1L);
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setEmployeeId(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTemplateIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setTemplateId(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTemplateNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setTemplateName(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setCreatedAt(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setUpdatedAt(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userBindingTemplate.setIsDeleted(null);

        // Create the UserBindingTemplate, which fails.
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllUserBindingTemplates() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        // Get all the userBindingTemplateList
        restUserBindingTemplateMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(userBindingTemplate.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].templateId").value(hasItem(DEFAULT_TEMPLATE_ID)))
            .andExpect(jsonPath("$.[*].templateName").value(hasItem(DEFAULT_TEMPLATE_NAME)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getUserBindingTemplate() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        // Get the userBindingTemplate
        restUserBindingTemplateMockMvc
            .perform(get(ENTITY_API_URL_ID, userBindingTemplate.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(userBindingTemplate.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.templateId").value(DEFAULT_TEMPLATE_ID))
            .andExpect(jsonPath("$.templateName").value(DEFAULT_TEMPLATE_NAME))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingUserBindingTemplate() throws Exception {
        // Get the userBindingTemplate
        restUserBindingTemplateMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUserBindingTemplate() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userBindingTemplate
        UserBindingTemplate updatedUserBindingTemplate = userBindingTemplateRepository.findById(userBindingTemplate.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedUserBindingTemplate are not directly saved in db
        em.detach(updatedUserBindingTemplate);
        updatedUserBindingTemplate
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .templateId(UPDATED_TEMPLATE_ID)
            .templateName(UPDATED_TEMPLATE_NAME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(updatedUserBindingTemplate);

        restUserBindingTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userBindingTemplateDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isOk());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUserBindingTemplateToMatchAllProperties(updatedUserBindingTemplate);
    }

    @Test
    @Transactional
    void putNonExistingUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userBindingTemplateDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userBindingTemplateDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUserBindingTemplateWithPatch() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userBindingTemplate using partial update
        UserBindingTemplate partialUpdatedUserBindingTemplate = new UserBindingTemplate();
        partialUpdatedUserBindingTemplate.setId(userBindingTemplate.getId());

        partialUpdatedUserBindingTemplate
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .templateId(UPDATED_TEMPLATE_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restUserBindingTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserBindingTemplate.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserBindingTemplate))
            )
            .andExpect(status().isOk());

        // Validate the UserBindingTemplate in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserBindingTemplateUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedUserBindingTemplate, userBindingTemplate),
            getPersistedUserBindingTemplate(userBindingTemplate)
        );
    }

    @Test
    @Transactional
    void fullUpdateUserBindingTemplateWithPatch() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userBindingTemplate using partial update
        UserBindingTemplate partialUpdatedUserBindingTemplate = new UserBindingTemplate();
        partialUpdatedUserBindingTemplate.setId(userBindingTemplate.getId());

        partialUpdatedUserBindingTemplate
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .templateId(UPDATED_TEMPLATE_ID)
            .templateName(UPDATED_TEMPLATE_NAME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restUserBindingTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserBindingTemplate.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserBindingTemplate))
            )
            .andExpect(status().isOk());

        // Validate the UserBindingTemplate in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserBindingTemplateUpdatableFieldsEquals(
            partialUpdatedUserBindingTemplate,
            getPersistedUserBindingTemplate(partialUpdatedUserBindingTemplate)
        );
    }

    @Test
    @Transactional
    void patchNonExistingUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, userBindingTemplateDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUserBindingTemplate() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userBindingTemplate.setId(longCount.incrementAndGet());

        // Create the UserBindingTemplate
        UserBindingTemplateDTO userBindingTemplateDTO = userBindingTemplateMapper.toDto(userBindingTemplate);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserBindingTemplateMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(userBindingTemplateDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserBindingTemplate in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUserBindingTemplate() throws Exception {
        // Initialize the database
        insertedUserBindingTemplate = userBindingTemplateRepository.saveAndFlush(userBindingTemplate);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the userBindingTemplate
        restUserBindingTemplateMockMvc
            .perform(delete(ENTITY_API_URL_ID, userBindingTemplate.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return userBindingTemplateRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected UserBindingTemplate getPersistedUserBindingTemplate(UserBindingTemplate userBindingTemplate) {
        return userBindingTemplateRepository.findById(userBindingTemplate.getId()).orElseThrow();
    }

    protected void assertPersistedUserBindingTemplateToMatchAllProperties(UserBindingTemplate expectedUserBindingTemplate) {
        assertUserBindingTemplateAllPropertiesEquals(
            expectedUserBindingTemplate,
            getPersistedUserBindingTemplate(expectedUserBindingTemplate)
        );
    }

    protected void assertPersistedUserBindingTemplateToMatchUpdatableProperties(UserBindingTemplate expectedUserBindingTemplate) {
        assertUserBindingTemplateAllUpdatablePropertiesEquals(
            expectedUserBindingTemplate,
            getPersistedUserBindingTemplate(expectedUserBindingTemplate)
        );
    }
}
